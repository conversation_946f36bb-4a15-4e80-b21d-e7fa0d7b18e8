import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { colors, spacing } from '@/constants/theme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Log to crash reporting service
    if (__DEV__) {
      console.error('Error details:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View style={styles.container}>
          <Text style={styles.title} variant="headlineSmall">
            Oups ! Une erreur s'est produite
          </Text>
          
          <Text style={styles.message} variant="bodyMedium">
            Nous sommes désolés, quelque chose s'est mal passé. Veuillez réessayer.
          </Text>

          {__DEV__ && this.state.error && (
            <View style={styles.errorDetails}>
              <Text style={styles.errorTitle} variant="titleSmall">
                Détails de l'erreur (mode développement) :
              </Text>
              <Text style={styles.errorText} variant="bodySmall">
                {this.state.error.message}
              </Text>
            </View>
          )}

          <Button
            mode="contained"
            onPress={this.handleRetry}
            style={styles.retryButton}
          >
            Réessayer
          </Button>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
    backgroundColor: colors.background,
  },
  title: {
    color: colors.error,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  message: {
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xl,
    lineHeight: 24,
  },
  errorDetails: {
    backgroundColor: colors.gray100,
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.xl,
    width: '100%',
  },
  errorTitle: {
    color: colors.error,
    marginBottom: spacing.sm,
  },
  errorText: {
    color: colors.textSecondary,
    fontFamily: 'monospace',
  },
  retryButton: {
    minWidth: 120,
  },
});
