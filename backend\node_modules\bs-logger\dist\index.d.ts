import { LogMeth<PERSON>, Logger, createLogger, lastSequenceNumber, resetSequence } from './logger';
import { LogContext, LogContexts } from './logger/context';
import { LogLevelName, LogLevels, logLevelNameFor, parseLogLevel } from './logger/level';
import { LogMessage, LogMessageFormatter, LogMessageTranslator, registerLogFormatter, resetLogFormatters } from './logger/message';
import { rootLogger, setup } from './logger/root';
import { DEFAULT_LOG_TARGET, LogTarget, parseLogTargets } from './logger/target';
import * as testing from './testing';
export { rootLogger as default, rootLogger as logger, setup, createLogger, DEFAULT_LOG_TARGET, lastSequenceNumber, LogContext, LogContexts, Logger, LogLevelName, logLevelNameFor, LogLevels, LogMessage, LogMessageFormatter, LogMessageTranslator, LogMethod, LogTarget, parseLogLevel, parseLogTargets, registerLogFormatter, resetLogFormatters, resetSequence, testing, };
