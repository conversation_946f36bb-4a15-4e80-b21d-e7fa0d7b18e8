export interface Location {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number | null;
  heading?: number | null;
  speed?: number | null;
  timestamp: number;
  address?: string;
  city?: string;
  country?: string;
}

export type LocationPermissionStatus = 
  | 'granted' 
  | 'denied' 
  | 'blocked' 
  | 'unknown';

export interface LocationError {
  code: number;
  message: string;
}

export interface GeofenceRegion {
  id: string;
  latitude: number;
  longitude: number;
  radius: number; // in meters
  notifyOnEntry?: boolean;
  notifyOnExit?: boolean;
}

export interface LocationUpdate {
  location: Location;
  timestamp: number;
  accuracy: number;
}

export interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface GeocodeResult {
  address_components: AddressComponent[];
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
    location_type: string;
    viewport: {
      northeast: { lat: number; lng: number };
      southwest: { lat: number; lng: number };
    };
  };
  place_id: string;
  types: string[];
}

export interface ReverseGeocodeResponse {
  results: GeocodeResult[];
  status: string;
}
