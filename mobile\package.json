{"name": "nowee-mobile", "version": "1.0.0", "description": "Application mobile Nowee - Entraide locale intelligente", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "clean": "react-native clean"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.7", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "zustand": "^4.4.7", "react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "@babel/runtime": "^7.23.5", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.2", "@types/react": "^18.2.42", "@types/react-native": "^0.72.7", "typescript": "^5.3.3", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.76.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}