{"name": "nowee-mobile", "version": "1.0.0", "description": "Application mobile Nowee - Entraide locale intelligente", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json}\"", "typecheck": "tsc --noEmit", "clean": "react-native clean", "reset-cache": "react-native start --reset-cache", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace NoweeApp.xcworkspace -scheme NoweeApp -configuration Release -destination generic/platform=iOS -archivePath NoweeApp.xcarchive archive"}, "dependencies": {"react": "18.3.1", "react-native": "0.72.7", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.6.0", "zustand": "^4.4.7", "react-native-maps": "^1.8.0", "react-native-geolocation-service": "^5.3.1", "react-native-permissions": "^3.10.1", "react-native-voice": "^3.2.4", "react-native-sound": "^0.11.2", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-fs": "^2.20.0", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^13.14.0", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.7", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-async-storage": "@react-native-async-storage/async-storage", "react-native-netinfo": "@react-native-community/netinfo", "react-native-device-info": "^10.11.0", "react-native-config": "^1.5.1", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-native-paper": "^5.11.3", "react-native-elements": "^3.4.3", "react-native-animatable": "^1.3.3", "react-native-super-grid": "^4.9.6", "react-native-swipe-gestures": "^1.0.5", "react-native-share": "^10.0.2", "react-native-contacts": "^7.0.8", "react-native-calendar-picker": "^7.1.4", "react-native-slider": "@react-native-community/slider", "react-native-progress": "^5.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-camera": "^4.2.1", "react-native-push-notification": "^8.1.1", "@react-native-firebase/app": "^18.6.1", "@react-native-firebase/messaging": "^18.6.1", "@react-native-firebase/analytics": "^18.6.1", "react-native-localize": "^3.0.2", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "@babel/runtime": "^7.23.5", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.2", "@types/react": "^18.2.42", "@types/react-native": "^0.72.7", "@types/react-test-renderer": "^18.0.7", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-jest": "^29.7.0", "eslint": "^8.55.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.76.8", "prettier": "^3.1.0", "react-test-renderer": "18.2.0", "typescript": "^5.3.3", "detox": "^20.13.5", "@testing-library/react-native": "^12.4.2", "@testing-library/jest-native": "^5.4.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["react-native", "nowee", "mobile", "entraide", "communauté", "afrique"], "author": "Nowee Team", "license": "MIT"}