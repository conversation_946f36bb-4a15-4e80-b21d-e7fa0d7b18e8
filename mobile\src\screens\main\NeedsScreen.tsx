import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { colors, spacing } from '@/constants/theme';

export const NeedsScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text variant="headlineMedium">Besoins</Text>
      <Text variant="bodyMedium" style={styles.subtitle}>
        Découvrez les besoins d'aide dans votre quartier
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.lg,
    backgroundColor: colors.background,
  },
  subtitle: {
    marginTop: spacing.sm,
    color: colors.textSecondary,
  },
});
