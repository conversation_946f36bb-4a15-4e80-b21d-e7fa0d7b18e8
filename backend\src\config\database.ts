import mongoose from 'mongoose';
import { logger } from '@/utils/logger';

export const connectDatabase = async (): Promise<void> => {
  try {
    const mongoUri = process.env.NODE_ENV === 'test' 
      ? process.env.MONGODB_TEST_URI 
      : process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MONGODB_URI n\'est pas défini dans les variables d\'environnement');
    }

    const options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false,
    };

    await mongoose.connect(mongoUri, options);

    mongoose.connection.on('connected', () => {
      logger.info('✅ Connexion MongoDB établie');
    });

    mongoose.connection.on('error', (error) => {
      logger.error('❌ Erreur MongoDB:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('⚠️ Connexion MongoDB fermée');
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('🔌 Connexion MongoDB fermée via SIGINT');
    });

  } catch (error) {
    logger.error('❌ Erreur de connexion MongoDB:', error);
    throw error;
  }
};

export const disconnectDatabase = async (): Promise<void> => {
  try {
    await mongoose.connection.close();
    logger.info('🔌 Connexion MongoDB fermée');
  } catch (error) {
    logger.error('❌ Erreur lors de la fermeture MongoDB:', error);
    throw error;
  }
};
