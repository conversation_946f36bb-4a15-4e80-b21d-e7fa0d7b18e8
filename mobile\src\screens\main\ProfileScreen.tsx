import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { useAuthStore } from '@/stores/authStore';
import { colors, spacing } from '@/constants/theme';

export const ProfileScreen: React.FC = () => {
  const { user, logout } = useAuthStore();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <View style={styles.container}>
      <Text variant="headlineMedium">Profil</Text>
      <Text variant="bodyMedium" style={styles.subtitle}>
        Bonjour {user?.firstName} {user?.lastName}
      </Text>
      
      <Button mode="outlined" onPress={handleLogout} style={styles.logoutButton}>
        Se déconnecter
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.lg,
    backgroundColor: colors.background,
  },
  subtitle: {
    marginTop: spacing.sm,
    marginBottom: spacing.xl,
    color: colors.textSecondary,
  },
  logoutButton: {
    marginTop: spacing.lg,
  },
});
