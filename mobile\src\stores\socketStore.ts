import { create } from 'zustand';
import { io, Socket } from 'socket.io-client';
import { useAuthStore } from './authStore';
import { Message, Conversation } from '@/types/chat';
import { Need } from '@/types/need';

interface SocketState {
  socket: Socket | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  
  // Real-time data
  onlineUsers: string[];
  typingUsers: { [conversationId: string]: string[] };
  
  // Actions
  initialize: () => void;
  disconnect: () => void;
  reconnect: () => void;
  
  // Chat events
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  sendMessage: (conversationId: string, message: Omit<Message, 'id' | 'timestamp'>) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  
  // Need events
  subscribeToArea: (lat: number, lng: number, radius: number) => void;
  unsubscribeFromArea: (lat: number, lng: number, radius: number) => void;
  notifyNeedCreated: (need: Need) => void;
  
  // Location events
  updateLocation: (lat: number, lng: number, address?: string) => void;
  shareLocation: (userId: string, duration?: number) => void;
  
  // Event handlers
  onNewMessage: (callback: (message: Message) => void) => void;
  onNewNeed: (callback: (need: Need) => void) => void;
  onUserStatusUpdate: (callback: (userId: string, status: string) => void) => void;
  onTypingUpdate: (callback: (conversationId: string, users: string[]) => void) => void;
}

export const useSocketStore = create<SocketState>((set, get) => ({
  socket: null,
  isConnected: false,
  isConnecting: false,
  error: null,
  onlineUsers: [],
  typingUsers: {},

  initialize: () => {
    const { token } = useAuthStore.getState();
    if (!token) return;

    set({ isConnecting: true, error: null });

    const socket = io(process.env.SOCKET_URL || 'http://localhost:3000', {
      auth: {
        token,
      },
      transports: ['websocket'],
      timeout: 20000,
    });

    // Connection events
    socket.on('connect', () => {
      console.log('Socket connected');
      set({ 
        socket, 
        isConnected: true, 
        isConnecting: false, 
        error: null 
      });
    });

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      set({ 
        isConnected: false, 
        isConnecting: false,
        onlineUsers: [],
        typingUsers: {}
      });
    });

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      set({ 
        error: 'Erreur de connexion temps réel',
        isConnecting: false,
        isConnected: false
      });
    });

    // User status events
    socket.on('user:status_update', (data: { userId: string; status: string }) => {
      const { onlineUsers } = get();
      if (data.status === 'online') {
        set({ onlineUsers: [...onlineUsers.filter(id => id !== data.userId), data.userId] });
      } else {
        set({ onlineUsers: onlineUsers.filter(id => id !== data.userId) });
      }
    });

    // Typing events
    socket.on('typing:start', (data: { userId: string; conversationId: string }) => {
      const { typingUsers } = get();
      const conversationTyping = typingUsers[data.conversationId] || [];
      if (!conversationTyping.includes(data.userId)) {
        set({
          typingUsers: {
            ...typingUsers,
            [data.conversationId]: [...conversationTyping, data.userId]
          }
        });
      }
    });

    socket.on('typing:stop', (data: { userId: string; conversationId: string }) => {
      const { typingUsers } = get();
      const conversationTyping = typingUsers[data.conversationId] || [];
      set({
        typingUsers: {
          ...typingUsers,
          [data.conversationId]: conversationTyping.filter(id => id !== data.userId)
        }
      });
    });

    set({ socket });
  },

  disconnect: () => {
    const { socket } = get();
    if (socket) {
      socket.disconnect();
      set({ 
        socket: null, 
        isConnected: false, 
        isConnecting: false,
        onlineUsers: [],
        typingUsers: {}
      });
    }
  },

  reconnect: () => {
    get().disconnect();
    setTimeout(() => {
      get().initialize();
    }, 1000);
  },

  // Chat methods
  joinConversation: (conversationId: string) => {
    const { socket } = get();
    socket?.emit('chat:join', { conversationId });
  },

  leaveConversation: (conversationId: string) => {
    const { socket } = get();
    socket?.emit('chat:leave', { conversationId });
  },

  sendMessage: (conversationId: string, message: Omit<Message, 'id' | 'timestamp'>) => {
    const { socket } = get();
    socket?.emit('chat:message', {
      conversationId,
      ...message
    });
  },

  startTyping: (conversationId: string) => {
    const { socket } = get();
    socket?.emit('typing:start', { conversationId });
  },

  stopTyping: (conversationId: string) => {
    const { socket } = get();
    socket?.emit('typing:stop', { conversationId });
  },

  // Need methods
  subscribeToArea: (lat: number, lng: number, radius: number) => {
    const { socket } = get();
    socket?.emit('needs:subscribe_area', { lat, lng, radius });
  },

  unsubscribeFromArea: (lat: number, lng: number, radius: number) => {
    const { socket } = get();
    socket?.emit('needs:unsubscribe_area', { lat, lng, radius });
  },

  notifyNeedCreated: (need: Need) => {
    const { socket } = get();
    socket?.emit('needs:created', {
      needId: need.id,
      title: need.title,
      category: need.category,
      urgency: need.urgency,
      location: need.location,
      radius: need.radius
    });
  },

  // Location methods
  updateLocation: (lat: number, lng: number, address?: string) => {
    const { socket } = get();
    socket?.emit('location:update', {
      coordinates: [lng, lat],
      address
    });
  },

  shareLocation: (userId: string, duration?: number) => {
    const { socket } = get();
    socket?.emit('location:share_request', {
      targetUserId: userId,
      duration
    });
  },

  // Event handlers
  onNewMessage: (callback: (message: Message) => void) => {
    const { socket } = get();
    socket?.on('chat:new_message', callback);
  },

  onNewNeed: (callback: (need: Need) => void) => {
    const { socket } = get();
    socket?.on('needs:new_need', callback);
  },

  onUserStatusUpdate: (callback: (userId: string, status: string) => void) => {
    const { socket } = get();
    socket?.on('user:status_update', (data) => callback(data.userId, data.status));
  },

  onTypingUpdate: (callback: (conversationId: string, users: string[]) => void) => {
    const { socket } = get();
    socket?.on('typing:start', (data) => {
      const { typingUsers } = get();
      callback(data.conversationId, typingUsers[data.conversationId] || []);
    });
    socket?.on('typing:stop', (data) => {
      const { typingUsers } = get();
      callback(data.conversationId, typingUsers[data.conversationId] || []);
    });
  },
}));
