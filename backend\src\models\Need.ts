import mongoose, { Document, Schema } from 'mongoose';
import { ILocation } from './User';

export interface INeed extends Document {
  _id: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  type: 'service' | 'object' | 'skill' | 'help';
  location: ILocation;
  radius: number; // search radius in kilometers
  compensation: {
    type: 'free' | 'nowcoins' | 'money' | 'exchange';
    amount?: number;
    description?: string;
  };
  status: 'active' | 'matched' | 'completed' | 'cancelled' | 'expired';
  tags: string[];
  images?: string[];
  expiresAt: Date;
  matchedWith?: mongoose.Types.ObjectId; // User who will help
  completedAt?: Date;
  rating?: {
    score: number;
    comment?: string;
    ratedBy: mongoose.Types.ObjectId;
  };
  views: number;
  responses: number;
  createdAt: Date;
  updatedAt: Date;
}

const NeedSchema = new Schema<INeed>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'transport',
      'bricolage',
      'menage',
      'cuisine',
      'garde',
      'courses',
      'informatique',
      'education',
      'sante',
      'juridique',
      'autre'
    ],
    index: true,
  },
  subcategory: {
    type: String,
    trim: true,
  },
  urgency: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true,
  },
  type: {
    type: String,
    enum: ['service', 'object', 'skill', 'help'],
    required: true,
    index: true,
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point',
    },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(coordinates: number[]) {
          return coordinates.length === 2 &&
                 coordinates[0] >= -180 && coordinates[0] <= 180 &&
                 coordinates[1] >= -90 && coordinates[1] <= 90;
        },
        message: 'Coordonnées invalides',
      },
    },
    address: String,
    city: String,
    country: String,
  },
  radius: {
    type: Number,
    required: true,
    min: 1,
    max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50'),
    default: parseInt(process.env.DEFAULT_SEARCH_RADIUS_KM || '5'),
  },
  compensation: {
    type: {
      type: String,
      enum: ['free', 'nowcoins', 'money', 'exchange'],
      default: 'free',
    },
    amount: {
      type: Number,
      min: 0,
    },
    description: {
      type: String,
      maxlength: 200,
    },
  },
  status: {
    type: String,
    enum: ['active', 'matched', 'completed', 'cancelled', 'expired'],
    default: 'active',
    index: true,
  },
  tags: {
    type: [String],
    default: [],
    validate: {
      validator: function(tags: string[]) {
        return tags.length <= 10;
      },
      message: 'Maximum 10 tags autorisés',
    },
  },
  images: {
    type: [String],
    default: [],
    validate: {
      validator: function(images: string[]) {
        return images.length <= 5;
      },
      message: 'Maximum 5 images autorisées',
    },
  },
  expiresAt: {
    type: Date,
    required: true,
    default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    index: { expireAfterSeconds: 0 },
  },
  matchedWith: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  completedAt: {
    type: Date,
    default: null,
  },
  rating: {
    score: {
      type: Number,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
      maxlength: 500,
    },
    ratedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  views: {
    type: Number,
    default: 0,
    min: 0,
  },
  responses: {
    type: Number,
    default: 0,
    min: 0,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
NeedSchema.index({ location: '2dsphere' });
NeedSchema.index({ user: 1, status: 1 });
NeedSchema.index({ category: 1, type: 1 });
NeedSchema.index({ urgency: 1, createdAt: -1 });
NeedSchema.index({ status: 1, expiresAt: 1 });
NeedSchema.index({ tags: 1 });
NeedSchema.index({ 'compensation.type': 1 });

// Compound indexes for common queries
NeedSchema.index({ status: 1, location: '2dsphere', category: 1 });
NeedSchema.index({ status: 1, urgency: 1, createdAt: -1 });

// Virtual for time remaining
NeedSchema.virtual('timeRemaining').get(function() {
  if (this.status !== 'active') return null;
  const now = new Date();
  const remaining = this.expiresAt.getTime() - now.getTime();
  return remaining > 0 ? remaining : 0;
});

// Virtual for is expired
NeedSchema.virtual('isExpired').get(function() {
  return new Date() > this.expiresAt;
});

// Pre-save middleware to update status if expired
NeedSchema.pre('save', function(next) {
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
  }
  next();
});

// Method to increment views
NeedSchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Method to increment responses
NeedSchema.methods.incrementResponses = function() {
  this.responses += 1;
  return this.save();
};

export const Need = mongoose.model<INeed>('Need', NeedSchema);
