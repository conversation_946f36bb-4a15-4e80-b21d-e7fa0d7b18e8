# API Configuration
API_BASE_URL=http://localhost:3000/api
SOCKET_URL=http://localhost:3000

# Environment
NODE_ENV=development

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_APP_ID=your-firebase-app-id
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_DATABASE_URL=https://your-project.firebaseio.com
FIREBASE_STORAGE_BUCKET=your-project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your-sender-id

# OpenAI (for local AI features)
OPENAI_API_KEY=sk-your-openai-api-key

# App Configuration
APP_NAME=Nowee
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# Features Flags
ENABLE_VOICE_RECOGNITION=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true

# Debug
DEBUG_MODE=true
LOG_LEVEL=debug

# Geolocation
DEFAULT_LATITUDE=14.6928
DEFAULT_LONGITUDE=-17.4467
DEFAULT_SEARCH_RADIUS=5

# Cache
CACHE_DURATION_MINUTES=30
MAX_CACHE_SIZE_MB=50

# Upload
MAX_IMAGE_SIZE_MB=5
MAX_AUDIO_SIZE_MB=10
COMPRESSION_QUALITY=0.8

# Languages
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,wo,ff,en

# Notifications
ENABLE_PUSH_NOTIFICATIONS=true
NOTIFICATION_SOUND=default

# Security
ENABLE_BIOMETRIC_AUTH=true
SESSION_TIMEOUT_MINUTES=60
AUTO_LOGOUT_MINUTES=120
