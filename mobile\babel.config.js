module.exports = {
  presets: ['module:metro-react-native-babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './src',
          '@/components': './src/components',
          '@/screens': './src/screens',
          '@/services': './src/services',
          '@/stores': './src/stores',
          '@/types': './src/types',
          '@/utils': './src/utils',
          '@/hooks': './src/hooks',
          '@/constants': './src/constants',
          '@/assets': './assets',
        },
      },
    ],
    'react-native-reanimated/plugin',
  ],
};
