name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Tests Backend
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd backend
        npm ci

    - name: Run backend linting
      run: |
        cd backend
        npm run lint

    - name: Run backend type checking
      run: |
        cd backend
        npm run typecheck

    - name: Run backend tests
      run: |
        cd backend
        npm run test:coverage
      env:
        NODE_ENV: test
        MONGODB_TEST_URI: ********************************************************************
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-jwt-secret-for-ci
        JWT_REFRESH_SECRET: test-refresh-secret-for-ci

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  # Tests Mobile
  test-mobile:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Install mobile dependencies
      run: |
        cd mobile
        npm ci

    - name: Run mobile linting
      run: |
        cd mobile
        npm run lint

    - name: Run mobile type checking
      run: |
        cd mobile
        npm run typecheck

    - name: Run mobile tests
      run: |
        cd mobile
        npm run test:coverage

    - name: Upload mobile coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: mobile/coverage/lcov.info
        flags: mobile
        name: mobile-coverage

  # Build Backend Docker Image
  build-backend:
    needs: test-backend
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  # Build Mobile Android
  build-android:
    needs: test-mobile
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile/package-lock.json

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v3

    - name: Install mobile dependencies
      run: |
        cd mobile
        npm ci

    - name: Create production environment file
      run: |
        cd mobile
        echo "API_BASE_URL=${{ secrets.PROD_API_BASE_URL }}" > .env.production
        echo "SOCKET_URL=${{ secrets.PROD_SOCKET_URL }}" >> .env.production
        echo "GOOGLE_MAPS_API_KEY=${{ secrets.GOOGLE_MAPS_API_KEY }}" >> .env.production

    - name: Build Android APK
      run: |
        cd mobile/android
        ./gradlew assembleRelease

    - name: Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: android-apk
        path: mobile/android/app/build/outputs/apk/release/app-release.apk

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    needs: [build-backend, build-android]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment commands here

  # Deploy to Production
  deploy-production:
    needs: [build-backend, build-android, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add actual deployment commands here

  # Notify on Slack
  notify:
    needs: [test-backend, test-mobile]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
