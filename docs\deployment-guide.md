# Guide de Déploiement - Nowee

## 🚀 Vue d'Ensemble

Ce guide couvre le déploiement de Nowee en production, incluant le backend API, l'application mobile, et l'infrastructure nécessaire.

## 🏗️ Architecture de Production

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   CDN/CloudFront│    │   Mobile Apps   │
│   (AWS ALB)     │    │   (Static Assets)│    │   (App Stores)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   File Storage  │    │   Push Notifs   │
│   (Kong/AWS)    │    │   (AWS S3)      │    │   (Firebase)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backend API   │    │   Database      │    │   Cache         │
│   (ECS/K8s)     │◄──►│   (MongoDB)     │◄──►│   (Redis)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 Prérequis

### Infrastructure
- **Cloud Provider** : AWS (recommandé) ou Google Cloud
- **Container Registry** : Docker Hub ou AWS ECR
- **CI/CD** : GitHub Actions (configuré)
- **Monitoring** : CloudWatch, Datadog, ou Sentry
- **DNS** : Route 53 ou CloudFlare

### Comptes et Services
- Compte AWS avec permissions appropriées
- Compte MongoDB Atlas (ou instance MongoDB)
- Compte Redis Cloud (ou instance Redis)
- Compte Firebase (notifications push)
- Compte OpenAI (IA)
- Compte Twilio (SMS)

## 🐳 Déploiement Backend

### 1. Préparation de l'Image Docker

```bash
# Build de l'image de production
cd backend
docker build -t nowee-backend:latest .

# Tag pour le registry
docker tag nowee-backend:latest your-registry/nowee-backend:v1.0.0

# Push vers le registry
docker push your-registry/nowee-backend:v1.0.0
```

### 2. Configuration des Variables d'Environnement

Créez un fichier `.env.production` :

```bash
# Application
NODE_ENV=production
PORT=3000

# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/nowee
REDIS_URL=redis://user:password@redis-cluster:6379

# JWT
JWT_SECRET=your-super-secure-jwt-secret-256-bits
JWT_EXPIRES_IN=7d

# External Services
OPENAI_API_KEY=sk-your-openai-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token

# AWS
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
AWS_S3_BUCKET=nowee-production-uploads

# Firebase
FIREBASE_PROJECT_ID=nowee-production
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Security
CORS_ORIGIN=https://nowee.app,https://admin.nowee.app
RATE_LIMIT_MAX_REQUESTS=1000
```

### 3. Déploiement sur AWS ECS

#### Fichier `docker-compose.prod.yml`

```yaml
version: '3.8'
services:
  backend:
    image: your-registry/nowee-backend:v1.0.0
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: awslogs
      options:
        awslogs-group: /ecs/nowee-backend
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
```

#### Task Definition ECS

```json
{
  "family": "nowee-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "nowee-backend",
      "image": "your-registry/nowee-backend:v1.0.0",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "MONGODB_URI",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:nowee/mongodb-uri"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/nowee-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

### 4. Configuration du Load Balancer

```yaml
# ALB Target Group
TargetGroup:
  Type: AWS::ElasticLoadBalancingV2::TargetGroup
  Properties:
    Name: nowee-backend-tg
    Port: 3000
    Protocol: HTTP
    VpcId: !Ref VPC
    TargetType: ip
    HealthCheckPath: /health
    HealthCheckProtocol: HTTP
    HealthCheckIntervalSeconds: 30
    HealthyThresholdCount: 2
    UnhealthyThresholdCount: 5
```

## 📱 Déploiement Mobile

### 1. Configuration de l'Environnement

#### Fichier `mobile/.env.production`

```bash
API_BASE_URL=https://api.nowee.app
SOCKET_URL=https://api.nowee.app
NODE_ENV=production

GOOGLE_MAPS_API_KEY=your-production-google-maps-key
FIREBASE_PROJECT_ID=nowee-production
FIREBASE_APP_ID=your-firebase-app-id

ENABLE_ANALYTICS=true
ENABLE_CRASH_REPORTING=true
LOG_LEVEL=warn
```

### 2. Build Android

```bash
cd mobile

# Nettoyer les builds précédents
npm run clean

# Générer le bundle de production
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle

# Build de l'APK de production
cd android
./gradlew assembleRelease

# Ou build de l'AAB pour Google Play
./gradlew bundleRelease
```

### 3. Build iOS

```bash
cd mobile

# Installer les pods
cd ios && pod install && cd ..

# Build pour l'App Store
npx react-native run-ios --configuration Release

# Ou via Xcode
# Ouvrir ios/NoweeApp.xcworkspace dans Xcode
# Product > Archive
```

### 4. Déploiement sur les Stores

#### Google Play Store

1. Créer un compte développeur Google Play
2. Configurer l'application dans la console
3. Uploader l'AAB généré
4. Configurer les métadonnées (descriptions, captures d'écran)
5. Soumettre pour review

#### Apple App Store

1. Créer un compte développeur Apple
2. Configurer l'app dans App Store Connect
3. Uploader via Xcode ou Application Loader
4. Configurer les métadonnées
5. Soumettre pour review

## 🔄 CI/CD avec GitHub Actions

### Fichier `.github/workflows/deploy.yml`

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
    tags: ['v*']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Build and push Docker image
        run: |
          docker build -t nowee-backend ./backend
          docker tag nowee-backend:latest $ECR_REGISTRY/nowee-backend:$GITHUB_SHA
          docker push $ECR_REGISTRY/nowee-backend:$GITHUB_SHA

      - name: Deploy to ECS
        run: |
          aws ecs update-service --cluster nowee-cluster --service nowee-backend --force-new-deployment

  deploy-mobile:
    needs: test
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Setup React Native
        run: |
          npm install -g react-native-cli
          cd mobile && npm ci

      - name: Build Android
        run: |
          cd mobile/android
          ./gradlew assembleRelease

      - name: Upload to Google Play
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}
          packageName: com.nowee.app
          releaseFiles: mobile/android/app/build/outputs/bundle/release/app-release.aab
          track: production
```

## 📊 Monitoring et Logging

### 1. CloudWatch Logs

```bash
# Créer les groupes de logs
aws logs create-log-group --log-group-name /ecs/nowee-backend
aws logs create-log-group --log-group-name /ecs/nowee-mobile
```

### 2. Métriques Personnalisées

```typescript
// Dans le backend
import { CloudWatch } from 'aws-sdk';

const cloudwatch = new CloudWatch();

export const trackMetric = async (metricName: string, value: number) => {
  await cloudwatch.putMetricData({
    Namespace: 'Nowee/API',
    MetricData: [{
      MetricName: metricName,
      Value: value,
      Unit: 'Count',
      Timestamp: new Date()
    }]
  }).promise();
};
```

### 3. Alertes

```yaml
# CloudFormation template pour les alertes
HighErrorRateAlarm:
  Type: AWS::CloudWatch::Alarm
  Properties:
    AlarmName: Nowee-HighErrorRate
    AlarmDescription: High error rate detected
    MetricName: ErrorRate
    Namespace: Nowee/API
    Statistic: Average
    Period: 300
    EvaluationPeriods: 2
    Threshold: 5
    ComparisonOperator: GreaterThanThreshold
    AlarmActions:
      - !Ref SNSTopic
```

## 🔒 Sécurité en Production

### 1. Secrets Management

```bash
# Stocker les secrets dans AWS Secrets Manager
aws secretsmanager create-secret \
  --name "nowee/mongodb-uri" \
  --description "MongoDB connection string" \
  --secret-string "mongodb+srv://..."
```

### 2. WAF Configuration

```yaml
WebACL:
  Type: AWS::WAFv2::WebACL
  Properties:
    Name: NoweeWebACL
    Scope: REGIONAL
    DefaultAction:
      Allow: {}
    Rules:
      - Name: RateLimitRule
        Priority: 1
        Statement:
          RateBasedStatement:
            Limit: 2000
            AggregateKeyType: IP
        Action:
          Block: {}
```

### 3. SSL/TLS

```bash
# Obtenir un certificat SSL via ACM
aws acm request-certificate \
  --domain-name api.nowee.app \
  --subject-alternative-names *.nowee.app \
  --validation-method DNS
```

## 🔄 Mise à Jour et Rollback

### Déploiement Blue-Green

```bash
# Créer un nouveau service avec la nouvelle version
aws ecs create-service \
  --cluster nowee-cluster \
  --service-name nowee-backend-green \
  --task-definition nowee-backend:2

# Basculer le trafic
aws elbv2 modify-listener \
  --listener-arn arn:aws:elasticloadbalancing:... \
  --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:...
```

### Rollback Rapide

```bash
# Revenir à la version précédente
aws ecs update-service \
  --cluster nowee-cluster \
  --service nowee-backend \
  --task-definition nowee-backend:1
```

## 📈 Optimisation des Performances

### 1. CDN Configuration

```yaml
CloudFrontDistribution:
  Type: AWS::CloudFront::Distribution
  Properties:
    DistributionConfig:
      Origins:
        - DomainName: api.nowee.app
          Id: api-origin
          CustomOriginConfig:
            HTTPPort: 443
            OriginProtocolPolicy: https-only
      DefaultCacheBehavior:
        TargetOriginId: api-origin
        ViewerProtocolPolicy: redirect-to-https
        CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
```

### 2. Auto Scaling

```yaml
AutoScalingTarget:
  Type: AWS::ApplicationAutoScaling::ScalableTarget
  Properties:
    ServiceNamespace: ecs
    ResourceId: service/nowee-cluster/nowee-backend
    ScalableDimension: ecs:service:DesiredCount
    MinCapacity: 2
    MaxCapacity: 10

ScalingPolicy:
  Type: AWS::ApplicationAutoScaling::ScalingPolicy
  Properties:
    PolicyName: NoweeScalingPolicy
    PolicyType: TargetTrackingScaling
    TargetTrackingScalingPolicyConfiguration:
      TargetValue: 70
      PredefinedMetricSpecification:
        PredefinedMetricType: ECSServiceAverageCPUUtilization
```

## 🆘 Dépannage

### Problèmes Courants

1. **Service ne démarre pas**
   - Vérifier les logs CloudWatch
   - Vérifier les variables d'environnement
   - Tester la connectivité à la base de données

2. **Erreurs 502/503**
   - Vérifier le health check
   - Vérifier la configuration du load balancer
   - Vérifier les limites de ressources

3. **Performance dégradée**
   - Analyser les métriques CloudWatch
   - Vérifier les requêtes de base de données
   - Optimiser les requêtes lentes

### Commandes Utiles

```bash
# Logs en temps réel
aws logs tail /ecs/nowee-backend --follow

# Status du service
aws ecs describe-services --cluster nowee-cluster --services nowee-backend

# Métriques
aws cloudwatch get-metric-statistics \
  --namespace Nowee/API \
  --metric-name RequestCount \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T23:59:59Z \
  --period 3600 \
  --statistics Sum
```

## 📞 Support

Pour le support de déploiement :
- Email : <EMAIL>
- Documentation : https://docs.nowee.app/deployment
- Slack : #deployment
