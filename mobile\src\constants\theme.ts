import { DefaultTheme } from 'react-native-paper';

export const colors = {
  primary: '#667eea',
  primaryDark: '#5a67d8',
  primaryLight: '#7c8aed',
  secondary: '#f093fb',
  secondaryDark: '#ed64a6',
  secondaryLight: '#f3a8fc',
  
  // Status colors
  success: '#48bb78',
  warning: '#ed8936',
  error: '#f56565',
  info: '#4299e1',
  
  // Neutral colors
  white: '#ffffff',
  black: '#000000',
  gray50: '#f7fafc',
  gray100: '#edf2f7',
  gray200: '#e2e8f0',
  gray300: '#cbd5e0',
  gray400: '#a0aec0',
  gray500: '#718096',
  gray600: '#4a5568',
  gray700: '#2d3748',
  gray800: '#1a202c',
  gray900: '#171923',
  
  // Background colors
  background: '#f7fafc',
  surface: '#ffffff',
  card: '#ffffff',
  
  // Text colors
  text: '#2d3748',
  textSecondary: '#718096',
  textLight: '#a0aec0',
  
  // Border colors
  border: '#e2e8f0',
  borderLight: '#f7fafc',
  
  // Nowee specific colors
  nowcoin: '#f6ad55',
  reputation: '#38b2ac',
  urgent: '#f56565',
  high: '#ed8936',
  medium: '#ecc94b',
  low: '#48bb78',
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

export const fontSize = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

export const fontWeight = {
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
};

export const shadows = {
  sm: {
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.secondary,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    disabled: colors.gray400,
    placeholder: colors.gray400,
    backdrop: 'rgba(0, 0, 0, 0.5)',
    onSurface: colors.text,
    notification: colors.error,
  },
  roundness: borderRadius.md,
  fonts: {
    ...DefaultTheme.fonts,
    regular: {
      fontFamily: 'System',
      fontWeight: fontWeight.normal,
    },
    medium: {
      fontFamily: 'System',
      fontWeight: fontWeight.medium,
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
  },
};

export const categoryColors = {
  transport: '#3498db',
  bricolage: '#e67e22',
  menage: '#2ecc71',
  cuisine: '#e74c3c',
  garde: '#f39c12',
  courses: '#9b59b6',
  informatique: '#34495e',
  education: '#16a085',
  sante: '#c0392b',
  juridique: '#8e44ad',
  autre: '#95a5a6',
};

export const urgencyColors = {
  low: colors.low,
  medium: colors.medium,
  high: colors.high,
  urgent: colors.urgent,
};
