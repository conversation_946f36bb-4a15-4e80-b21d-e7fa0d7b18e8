{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "sourceMap": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/stores/*": ["stores/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/hooks/*": ["hooks/*"], "@/constants/*": ["constants/*"], "@/assets/*": ["../assets/*"]}}, "include": ["src/**/*", "index.js", "metro.config.js", "babel.config.js"], "exclude": ["node_modules", "android", "ios", "dist", "build"]}