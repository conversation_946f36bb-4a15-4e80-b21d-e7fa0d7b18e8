import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput, Button, Card, Divider } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Toast from 'react-native-toast-message';

import { useAuthStore } from '@/stores/authStore';
import { RootStackParamList } from '@/navigation/AppNavigator';
import { colors, spacing } from '@/constants/theme';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const { login, isLoading, error, clearError } = useAuthStore();

  const [identifier, setIdentifier] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!identifier.trim() || !password.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Erreur',
        text2: 'Veuillez remplir tous les champs',
      });
      return;
    }

    try {
      clearError();
      await login({ identifier: identifier.trim(), password });
      Toast.show({
        type: 'success',
        text1: 'Connexion réussie',
        text2: 'Bienvenue sur Nowee !',
      });
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Erreur de connexion',
        text2: error || 'Vérifiez vos identifiants',
      });
    }
  };

  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  const navigateToForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  if (isLoading) {
    return <LoadingSpinner message="Connexion en cours..." />;
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text variant="displaySmall" style={styles.title}>
            Nowee
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            L'entraide locale intelligente
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Text variant="headlineSmall" style={styles.cardTitle}>
              Connexion
            </Text>

            <TextInput
              label="Email ou téléphone"
              value={identifier}
              onChangeText={setIdentifier}
              mode="outlined"
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              textContentType="emailAddress"
            />

            <TextInput
              label="Mot de passe"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              autoComplete="password"
              textContentType="password"
            />

            <Button
              mode="contained"
              onPress={handleLogin}
              style={styles.loginButton}
              loading={isLoading}
              disabled={isLoading}
            >
              Se connecter
            </Button>

            <Button
              mode="text"
              onPress={navigateToForgotPassword}
              style={styles.forgotButton}
            >
              Mot de passe oublié ?
            </Button>

            <Divider style={styles.divider} />

            <Text variant="bodyMedium" style={styles.registerText}>
              Pas encore de compte ?
            </Text>

            <Button
              mode="outlined"
              onPress={navigateToRegister}
              style={styles.registerButton}
            >
              S'inscrire
            </Button>
          </Card.Content>
        </Card>

        <Text variant="bodySmall" style={styles.footer}>
          En vous connectant, vous acceptez nos conditions d'utilisation
        </Text>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    color: colors.white,
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  card: {
    marginBottom: spacing.lg,
  },
  cardContent: {
    padding: spacing.xl,
  },
  cardTitle: {
    textAlign: 'center',
    marginBottom: spacing.xl,
    color: colors.text,
  },
  input: {
    marginBottom: spacing.md,
  },
  loginButton: {
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  forgotButton: {
    marginBottom: spacing.lg,
  },
  divider: {
    marginVertical: spacing.lg,
  },
  registerText: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.textSecondary,
  },
  registerButton: {
    borderColor: colors.primary,
  },
  footer: {
    textAlign: 'center',
    color: colors.white,
    opacity: 0.8,
  },
});
