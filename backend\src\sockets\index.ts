import { Server, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { User, IUser } from '@/models/User';
import { logger } from '@/utils/logger';
import { chatHandlers } from './chatHandlers';
import { needHandlers } from './needHandlers';
import { locationHandlers } from './locationHandlers';

interface AuthenticatedSocket extends Socket {
  user?: IUser;
  userId?: string;
}

// Socket authentication middleware
const authenticateSocket = async (socket: AuthenticatedSocket, next: Function) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return next(new Error('Token manquant'));
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      return next(new Error('Configuration JWT manquante'));
    }

    const decoded = jwt.verify(token, jwtSecret) as any;
    const user = await User.findById(decoded.userId);

    if (!user || !user.isActive) {
      return next(new Error('Utilisateur non trouvé ou inactif'));
    }

    socket.user = user;
    socket.userId = user._id.toString();
    
    // Update last seen
    user.lastSeen = new Date();
    await user.save();

    next();
  } catch (error) {
    logger.error('Erreur d\'authentification Socket.io:', error);
    next(new Error('Authentification échouée'));
  }
};

export const initializeSocketHandlers = (io: Server): void => {
  // Apply authentication middleware
  io.use(authenticateSocket);

  io.on('connection', (socket: AuthenticatedSocket) => {
    if (!socket.user) {
      socket.disconnect();
      return;
    }

    logger.info(`Utilisateur connecté: ${socket.user.email} (${socket.id})`);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Send welcome message
    socket.emit('connected', {
      message: 'Connexion établie',
      user: socket.user.toPublicJSON(),
    });

    // Register event handlers
    chatHandlers(io, socket);
    needHandlers(io, socket);
    locationHandlers(io, socket);

    // Handle user status
    socket.on('user:status', async (status: 'online' | 'away' | 'busy') => {
      try {
        if (!socket.user) return;

        // Update user status in database if needed
        // For now, we'll just broadcast to relevant rooms
        socket.broadcast.emit('user:status_update', {
          userId: socket.userId,
          status,
          lastSeen: new Date(),
        });
      } catch (error) {
        logger.error('Erreur mise à jour statut:', error);
      }
    });

    // Handle typing indicators
    socket.on('typing:start', (data: { conversationId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('typing:start', {
        userId: socket.userId,
        user: socket.user?.toPublicJSON(),
      });
    });

    socket.on('typing:stop', (data: { conversationId: string }) => {
      socket.to(`conversation:${data.conversationId}`).emit('typing:stop', {
        userId: socket.userId,
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`Utilisateur déconnecté: ${socket.user?.email} (${socket.id}) - Raison: ${reason}`);
      
      // Broadcast offline status
      socket.broadcast.emit('user:status_update', {
        userId: socket.userId,
        status: 'offline',
        lastSeen: new Date(),
      });
    });

    // Handle errors
    socket.on('error', (error) => {
      logger.error(`Erreur Socket.io pour ${socket.user?.email}:`, error);
    });
  });

  // Handle connection errors
  io.engine.on('connection_error', (error) => {
    logger.error('Erreur de connexion Socket.io:', error);
  });

  logger.info('🔌 Handlers Socket.io initialisés');
};
