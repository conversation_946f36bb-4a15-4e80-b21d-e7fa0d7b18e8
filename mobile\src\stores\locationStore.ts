import { create } from 'zustand';
import Geolocation from 'react-native-geolocation-service';
import { PERMISSIONS, request, check, RESULTS } from 'react-native-permissions';
import { Platform } from 'react-native';
import { Location, LocationPermissionStatus } from '@/types/location';

interface LocationState {
  currentLocation: Location | null;
  permissionStatus: LocationPermissionStatus;
  isLoading: boolean;
  error: string | null;
  watchId: number | null;
  
  // Actions
  requestPermission: () => Promise<LocationPermissionStatus>;
  getCurrentLocation: () => Promise<Location>;
  startWatching: () => Promise<void>;
  stopWatching: () => void;
  updateLocation: (location: Location) => void;
  clearError: () => void;
  initialize: () => Promise<void>;
}

const DEFAULT_LOCATION: Location = {
  latitude: parseFloat(process.env.DEFAULT_LATITUDE || '14.6928'), // Dakar
  longitude: parseFloat(process.env.DEFAULT_LONGITUDE || '-17.4467'),
  accuracy: 0,
  timestamp: Date.now(),
};

export const useLocationStore = create<LocationState>((set, get) => ({
  currentLocation: null,
  permissionStatus: 'unknown',
  isLoading: false,
  error: null,
  watchId: null,

  requestPermission: async () => {
    try {
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE 
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

      const result = await request(permission);
      
      let status: LocationPermissionStatus;
      switch (result) {
        case RESULTS.GRANTED:
          status = 'granted';
          break;
        case RESULTS.DENIED:
          status = 'denied';
          break;
        case RESULTS.BLOCKED:
          status = 'blocked';
          break;
        default:
          status = 'unknown';
      }

      set({ permissionStatus: status });
      return status;
    } catch (error: any) {
      set({ 
        error: error.message || 'Erreur de permission',
        permissionStatus: 'denied'
      });
      return 'denied';
    }
  },

  getCurrentLocation: async () => {
    return new Promise((resolve, reject) => {
      set({ isLoading: true, error: null });

      Geolocation.getCurrentPosition(
        (position) => {
          const location: Location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            altitude: position.coords.altitude,
            heading: position.coords.heading,
            speed: position.coords.speed,
            timestamp: position.timestamp,
          };

          set({ 
            currentLocation: location, 
            isLoading: false 
          });
          resolve(location);
        },
        (error) => {
          console.error('Geolocation error:', error);
          set({ 
            error: 'Impossible d\'obtenir la localisation',
            isLoading: false,
            currentLocation: DEFAULT_LOCATION
          });
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  },

  startWatching: async () => {
    const { permissionStatus } = get();
    
    if (permissionStatus !== 'granted') {
      const status = await get().requestPermission();
      if (status !== 'granted') {
        throw new Error('Permission de localisation refusée');
      }
    }

    // Stop existing watch if any
    get().stopWatching();

    const watchId = Geolocation.watchPosition(
      (position) => {
        const location: Location = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude,
          heading: position.coords.heading,
          speed: position.coords.speed,
          timestamp: position.timestamp,
        };

        set({ currentLocation: location });
      },
      (error) => {
        console.error('Watch position error:', error);
        set({ error: 'Erreur de suivi de localisation' });
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 10, // Update every 10 meters
        interval: 5000, // Update every 5 seconds
        fastestInterval: 2000,
      }
    );

    set({ watchId });
  },

  stopWatching: () => {
    const { watchId } = get();
    if (watchId !== null) {
      Geolocation.clearWatch(watchId);
      set({ watchId: null });
    }
  },

  updateLocation: (location: Location) => {
    set({ currentLocation: location });
  },

  clearError: () => set({ error: null }),

  initialize: async () => {
    try {
      // Check current permission status
      const permission = Platform.OS === 'ios' 
        ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE 
        : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

      const result = await check(permission);
      
      let status: LocationPermissionStatus;
      switch (result) {
        case RESULTS.GRANTED:
          status = 'granted';
          break;
        case RESULTS.DENIED:
          status = 'denied';
          break;
        case RESULTS.BLOCKED:
          status = 'blocked';
          break;
        default:
          status = 'unknown';
      }

      set({ permissionStatus: status });

      // If permission granted, get current location
      if (status === 'granted') {
        try {
          await get().getCurrentLocation();
        } catch (error) {
          console.warn('Failed to get initial location:', error);
          set({ currentLocation: DEFAULT_LOCATION });
        }
      } else {
        set({ currentLocation: DEFAULT_LOCATION });
      }
    } catch (error) {
      console.error('Location initialization error:', error);
      set({ 
        currentLocation: DEFAULT_LOCATION,
        permissionStatus: 'unknown'
      });
    }
  },
}));
