import express from 'express';
import { body } from 'express-validator';
import {
  processNeedRequest,
  getSuggestions,
  translateText,
  analyzeNeed
} from '@/controllers/aiController';
import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';

const router = express.Router();

// Process need validation
const processNeedValidation = [
  body('text')
    .trim()
    .isLength({ min: 5, max: 500 })
    .withMessage('Le texte doit contenir entre 5 et 500 caractères'),
  body('language')
    .optional()
    .isIn(['fr', 'wo', 'ff', 'en'])
    .withMessage('Langue invalide'),
  body('location')
    .optional()
    .isObject()
    .withMessage('Localisation invalide'),
];

// Translation validation
const translateValidation = [
  body('text')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Texte invalide'),
  body('from')
    .isIn(['fr', 'wo', 'ff', 'en'])
    .withMessage('Langue source invalide'),
  body('to')
    .isIn(['fr', 'wo', 'ff', 'en'])
    .withMessage('Langue cible invalide'),
];

// Analyze need validation
const analyzeNeedValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Titre invalide'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description invalide'),
];

// Routes
router.post('/process-need', optionalAuth, processNeedValidation, validateRequest, processNeedRequest);
router.get('/suggestions', authenticateToken, getSuggestions);
router.post('/translate', optionalAuth, translateValidation, validateRequest, translateText);
router.post('/analyze-need', optionalAuth, analyzeNeedValidation, validateRequest, analyzeNeed);

export default router;
