# Documentation API - Nowee

## 🌐 Vue d'Ensemble

L'API Nowee est une API REST qui permet aux applications clientes d'interagir avec la plateforme d'entraide locale. Elle utilise JSON pour les échanges de données et JWT pour l'authentification.

### URL de Base
- **Développement** : `http://localhost:3000/api`
- **Production** : `https://api.nowee.app`

### Authentification
L'API utilise des tokens JWT (JSON Web Tokens) pour l'authentification. Incluez le token dans l'en-tête Authorization :

```
Authorization: Bearer <votre-token-jwt>
```

## 📋 Endpoints Principaux

### 🔐 Authentification

#### POST /auth/register
Inscription d'un nouvel utilisateur.

**Corps de la requête :**
```json
{
  "email": "<EMAIL>",
  "phone": "+221771234567",
  "password": "motdepasse123",
  "firstName": "Prénom",
  "lastName": "Nom",
  "languages": ["fr", "wo"],
  "location": {
    "coordinates": [-17.4467, 14.6928],
    "address": "Dakar, Sénégal",
    "city": "Dakar",
    "country": "Sénégal"
  }
}
```

**Réponse :**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "Prénom",
    "lastName": "Nom",
    "isVerified": false,
    "nowcoins": 100
  },
  "token": "jwt_token_here"
}
```

#### POST /auth/login
Connexion d'un utilisateur existant.

**Corps de la requête :**
```json
{
  "identifier": "<EMAIL>", // email ou téléphone
  "password": "motdepasse123"
}
```

#### POST /auth/logout
Déconnexion de l'utilisateur (nécessite authentification).

#### POST /auth/refresh-token
Renouvellement du token JWT.

### 👤 Utilisateurs

#### GET /users/profile
Récupération du profil de l'utilisateur connecté.

**Réponse :**
```json
{
  "id": "user_id",
  "email": "<EMAIL>",
  "firstName": "Prénom",
  "lastName": "Nom",
  "avatar": "https://example.com/avatar.jpg",
  "bio": "Description de l'utilisateur",
  "languages": ["fr", "wo"],
  "location": {
    "coordinates": [-17.4467, 14.6928],
    "address": "Dakar, Sénégal"
  },
  "reputation": 85,
  "nowcoins": 150,
  "totalHelpsGiven": 12,
  "totalHelpsReceived": 8
}
```

#### PUT /users/profile
Mise à jour du profil utilisateur.

#### GET /users/search
Recherche d'utilisateurs.

**Paramètres de requête :**
- `q` : Terme de recherche
- `lat` : Latitude
- `lng` : Longitude
- `radius` : Rayon de recherche (km)
- `page` : Numéro de page
- `limit` : Nombre d'éléments par page

### 🆘 Besoins

#### POST /needs
Création d'un nouveau besoin.

**Corps de la requête :**
```json
{
  "title": "Besoin d'aide pour déménagement",
  "description": "Je cherche quelqu'un pour m'aider à déménager ce weekend",
  "category": "transport",
  "urgency": "medium",
  "type": "service",
  "location": {
    "coordinates": [-17.4467, 14.6928],
    "address": "Dakar, Sénégal"
  },
  "radius": 10,
  "compensation": {
    "type": "nowcoins",
    "amount": 50,
    "description": "50 NowCoins pour 4h d'aide"
  },
  "tags": ["déménagement", "transport", "weekend"],
  "expiresAt": "2024-01-15T18:00:00Z"
}
```

#### GET /needs
Récupération de la liste des besoins.

**Paramètres de requête :**
- `category` : Catégorie de besoin
- `urgency` : Niveau d'urgence
- `type` : Type de besoin
- `lat`, `lng`, `radius` : Filtrage géographique
- `page`, `limit` : Pagination

#### GET /needs/:id
Récupération d'un besoin spécifique.

#### PUT /needs/:id
Mise à jour d'un besoin (propriétaire uniquement).

#### DELETE /needs/:id
Suppression d'un besoin (propriétaire uniquement).

#### POST /needs/:id/respond
Répondre à un besoin.

**Corps de la requête :**
```json
{
  "message": "Je peux vous aider avec votre déménagement",
  "proposedCompensation": {
    "type": "nowcoins",
    "amount": 40
  }
}
```

### 💬 Chat

#### GET /chat
Récupération des conversations de l'utilisateur.

#### GET /chat/:conversationId
Récupération des messages d'une conversation.

#### POST /chat/:userId/message
Envoi d'un message à un utilisateur.

**Corps de la requête :**
```json
{
  "content": "Bonjour, j'ai vu votre besoin d'aide",
  "type": "text"
}
```

### 💰 NowCoins

#### GET /nowcoins/balance
Récupération du solde de NowCoins.

#### GET /nowcoins/transactions
Historique des transactions.

#### POST /nowcoins/transfer
Transfert de NowCoins.

**Corps de la requête :**
```json
{
  "recipientId": "user_id",
  "amount": 25,
  "description": "Paiement pour aide déménagement"
}
```

#### POST /nowcoins/daily-bonus
Récupération du bonus quotidien.

### 🤖 IA

#### POST /ai/process-need
Traitement intelligent d'un besoin exprimé en langage naturel.

**Corps de la requête :**
```json
{
  "text": "J'ai besoin de quelqu'un pour m'aider à réparer ma voiture demain matin",
  "language": "fr",
  "location": {
    "coordinates": [-17.4467, 14.6928]
  }
}
```

**Réponse :**
```json
{
  "analysis": {
    "category": "transport",
    "urgency": "medium",
    "type": "service",
    "suggestedTitle": "Aide pour réparation de voiture",
    "suggestedDescription": "Recherche assistance pour réparation automobile demain matin",
    "extractedKeywords": ["réparation", "voiture", "demain", "matin"]
  },
  "suggestions": [
    "Précisez le type de réparation nécessaire",
    "Indiquez si vous avez les outils nécessaires"
  ]
}
```

## 📊 Codes de Statut HTTP

- `200` : Succès
- `201` : Créé avec succès
- `400` : Requête invalide
- `401` : Non authentifié
- `403` : Accès interdit
- `404` : Ressource non trouvée
- `409` : Conflit (ex: email déjà utilisé)
- `422` : Entité non traitable (erreurs de validation)
- `429` : Trop de requêtes
- `500` : Erreur serveur interne

## 🔒 Sécurité

### Rate Limiting
- 100 requêtes par 15 minutes par IP
- Limites spéciales pour certains endpoints sensibles

### Validation
Toutes les données d'entrée sont validées côté serveur.

### CORS
L'API supporte CORS pour les domaines autorisés.

## 📝 Format des Erreurs

```json
{
  "success": false,
  "message": "Description de l'erreur",
  "errors": [
    {
      "field": "email",
      "message": "Email invalide",
      "value": "email-invalide"
    }
  ]
}
```

## 🔄 Pagination

Les endpoints qui retournent des listes utilisent la pagination :

```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 🌍 Géolocalisation

Les coordonnées sont au format [longitude, latitude] (GeoJSON).

Exemple :
```json
{
  "location": {
    "type": "Point",
    "coordinates": [-17.4467, 14.6928]
  }
}
```

## 📱 WebSocket (Socket.io)

### Connexion
```javascript
const socket = io('ws://localhost:3000', {
  auth: {
    token: 'votre-jwt-token'
  }
});
```

### Événements Principaux

#### Chat
- `chat:join` : Rejoindre une conversation
- `chat:message` : Envoyer un message
- `chat:new_message` : Nouveau message reçu
- `typing:start` / `typing:stop` : Indicateurs de frappe

#### Besoins
- `needs:subscribe_area` : S'abonner aux besoins d'une zone
- `needs:new_need` : Nouveau besoin dans la zone
- `needs:response` : Nouvelle réponse à un besoin

## 🧪 Tests

### Environnement de Test
- URL : `http://localhost:3000/api`
- Base de données : MongoDB de test
- Données de test disponibles

### Postman Collection
Une collection Postman est disponible dans `/docs/postman/`

## 📞 Support

Pour toute question sur l'API :
- Email : <EMAIL>
- Documentation interactive : https://api.nowee.app/docs
- GitHub Issues : https://github.com/nowee-app/nowee/issues
