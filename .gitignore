# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Build outputs
dist/
build/
coverage/
.nyc_output/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# React Native
mobile/android/app/build/
mobile/ios/build/
mobile/ios/Pods/
mobile/ios/*.xcworkspace
mobile/ios/*.xcuserdata
mobile/android/.gradle/
mobile/android/app/release/
mobile/metro.config.js.bak

# Expo
mobile/.expo/
mobile/expo-env.d.ts

# Temporary files
tmp/
temp/

# Database
*.sqlite
*.db

# Docker
.docker/

# Certificates
*.pem
*.key
*.crt

# Backup files
*.backup
*.bak

# Package manager
package-lock.json
yarn.lock

# Testing
.jest/
coverage/

# Documentation build
docs/_build/
docs/.doctrees/

# Local configuration
config/local.json
config/development.json
config/production.json
