import express from 'express';
import { body } from 'express-validator';
import { 
  register, 
  login, 
  logout, 
  refreshToken, 
  forgotPassword, 
  resetPassword,
  verifyEmail,
  verifyPhone,
  resendVerification
} from '@/controllers/authController';
import { authenticateToken } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';

const router = express.Router();

// Registration validation
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email invalide'),
  body('phone')
    .matches(/^\+[1-9]\d{1,14}$/)
    .withMessage('Numéro de téléphone invalide (format international requis)'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Le mot de passe doit contenir au moins 6 caractères')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  body('languages')
    .optional()
    .isArray()
    .withMessage('Les langues doivent être un tableau')
    .custom((languages) => {
      const validLanguages = ['fr', 'wo', 'ff', 'en'];
      return languages.every((lang: string) => validLanguages.includes(lang));
    })
    .withMessage('Langues invalides (fr, wo, ff, en autorisées)'),
];

// Login validation
const loginValidation = [
  body('identifier')
    .notEmpty()
    .withMessage('Email ou téléphone requis'),
  body('password')
    .notEmpty()
    .withMessage('Mot de passe requis'),
];

// Password reset validation
const forgotPasswordValidation = [
  body('identifier')
    .notEmpty()
    .withMessage('Email ou téléphone requis'),
];

const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('Token requis'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Le mot de passe doit contenir au moins 6 caractères')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre'),
];

// Verification validation
const verifyEmailValidation = [
  body('token')
    .notEmpty()
    .withMessage('Token requis'),
];

const verifyPhoneValidation = [
  body('code')
    .isLength({ min: 4, max: 6 })
    .isNumeric()
    .withMessage('Code de vérification invalide'),
];

// Routes
router.post('/register', registerValidation, validateRequest, register);
router.post('/login', loginValidation, validateRequest, login);
router.post('/logout', authenticateToken, logout);
router.post('/refresh-token', refreshToken);
router.post('/forgot-password', forgotPasswordValidation, validateRequest, forgotPassword);
router.post('/reset-password', resetPasswordValidation, validateRequest, resetPassword);
router.post('/verify-email', verifyEmailValidation, validateRequest, verifyEmail);
router.post('/verify-phone', authenticateToken, verifyPhoneValidation, validateRequest, verifyPhone);
router.post('/resend-verification', authenticateToken, resendVerification);

export default router;
