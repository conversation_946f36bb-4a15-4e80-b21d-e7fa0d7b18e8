import { User, Location } from './auth';

export interface Need {
  id: string;
  user: User | string;
  title: string;
  description: string;
  category: NeedCategory;
  subcategory?: string;
  urgency: NeedUrgency;
  type: NeedType;
  location: Location;
  radius: number;
  compensation: Compensation;
  status: NeedStatus;
  tags: string[];
  images?: string[];
  expiresAt: string;
  matchedWith?: User | string;
  completedAt?: string;
  rating?: Rating;
  views: number;
  responses: number;
  createdAt: string;
  updatedAt: string;
  
  // Virtual fields
  timeRemaining?: number;
  isExpired?: boolean;
  distance?: number; // Distance from user's location
}

export type NeedCategory = 
  | 'transport'
  | 'bricolage'
  | 'menage'
  | 'cuisine'
  | 'garde'
  | 'courses'
  | 'informatique'
  | 'education'
  | 'sante'
  | 'juridique'
  | 'autre';

export type NeedUrgency = 'low' | 'medium' | 'high' | 'urgent';

export type NeedType = 'service' | 'object' | 'skill' | 'help';

export type NeedStatus = 'active' | 'matched' | 'completed' | 'cancelled' | 'expired';

export interface Compensation {
  type: 'free' | 'nowcoins' | 'money' | 'exchange';
  amount?: number;
  description?: string;
}

export interface Rating {
  score: number; // 1-5
  comment?: string;
  ratedBy: User | string;
  ratedAt: string;
}

export interface NeedResponse {
  id: string;
  need: Need | string;
  responder: User | string;
  message: string;
  proposedCompensation?: Compensation;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface CreateNeedData {
  title: string;
  description: string;
  category: NeedCategory;
  subcategory?: string;
  urgency: NeedUrgency;
  type: NeedType;
  location: {
    coordinates: [number, number];
    address?: string;
    city?: string;
    country?: string;
  };
  radius: number;
  compensation: Compensation;
  tags?: string[];
  images?: string[];
  expiresAt?: string;
}

export interface UpdateNeedData {
  title?: string;
  description?: string;
  urgency?: NeedUrgency;
  compensation?: Compensation;
  tags?: string[];
  status?: NeedStatus;
}

export interface NeedSearchParams {
  q?: string;
  category?: NeedCategory;
  type?: NeedType;
  urgency?: NeedUrgency;
  compensation?: 'free' | 'nowcoins' | 'money' | 'exchange';
  lat?: number;
  lng?: number;
  radius?: number;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'urgency' | 'distance' | 'views';
  sortOrder?: 'asc' | 'desc';
}

export interface NeedSearchResponse {
  needs: Need[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}
