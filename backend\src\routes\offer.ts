import express from 'express';
import { body, param, query } from 'express-validator';
import {
  createOffer,
  getOffers,
  getOfferById,
  updateOffer,
  deleteOffer,
  searchOffers,
  getUserOffers
} from '@/controllers/offerController';
import { authenticateToken, optionalAuth, requireVerification } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import { upload } from '@/middleware/upload';

const router = express.Router();

// Create offer validation
const createOfferValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Le titre doit contenir entre 5 et 100 caractères'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('La description doit contenir entre 10 et 1000 caractères'),
  body('category')
    .isIn(['transport', 'bricolage', 'menage', 'cuisine', 'garde', 'courses', 'informatique', 'education', 'sante', 'juridique', 'autre'])
    .withMessage('Catégorie invalide'),
  body('type')
    .isIn(['service', 'object', 'skill', 'help'])
    .withMessage('Type invalide'),
  body('location.coordinates')
    .isArray({ min: 2, max: 2 })
    .withMessage('Coordonnées invalides')
    .custom((coordinates) => {
      const [lng, lat] = coordinates;
      return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
    })
    .withMessage('Coordonnées hors limites'),
  body('compensation.type')
    .isIn(['free', 'nowcoins', 'money', 'exchange'])
    .withMessage('Type de compensation invalide'),
  body('compensation.amount')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Montant invalide'),
  body('availability')
    .optional()
    .isObject()
    .withMessage('Disponibilité invalide'),
  body('tags')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Maximum 10 tags autorisés'),
];

// Search validation
const searchOffersValidation = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Recherche trop courte'),
  query('category')
    .optional()
    .isIn(['transport', 'bricolage', 'menage', 'cuisine', 'garde', 'courses', 'informatique', 'education', 'sante', 'juridique', 'autre'])
    .withMessage('Catégorie invalide'),
  query('type')
    .optional()
    .isIn(['service', 'object', 'skill', 'help'])
    .withMessage('Type invalide'),
  query('compensation')
    .optional()
    .isIn(['free', 'nowcoins', 'money', 'exchange'])
    .withMessage('Type de compensation invalide'),
  query('lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude invalide'),
  query('lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude invalide'),
  query('radius')
    .optional()
    .isInt({ min: 1, max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50') })
    .withMessage('Rayon invalide'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limite invalide'),
];

// ID validation
const offerIdValidation = [
  param('id')
    .isMongoId()
    .withMessage('ID offre invalide'),
];

// Routes
router.post('/', authenticateToken, requireVerification, upload.array('images', 5), createOfferValidation, validateRequest, createOffer);
router.get('/', optionalAuth, getOffers);
router.get('/search', optionalAuth, searchOffersValidation, validateRequest, searchOffers);
router.get('/my-offers', authenticateToken, getUserOffers);
router.get('/:id', optionalAuth, offerIdValidation, validateRequest, getOfferById);
router.put('/:id', authenticateToken, offerIdValidation, validateRequest, updateOffer);
router.delete('/:id', authenticateToken, offerIdValidation, validateRequest, deleteOffer);

export default router;
