import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput, Button, Card } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Toast from 'react-native-toast-message';

import { useAuthStore } from '@/stores/authStore';
import { useLocationStore } from '@/stores/locationStore';
import { RootStackParamList } from '@/navigation/AppNavigator';
import { colors, spacing } from '@/constants/theme';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';

type RegisterScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Register'>;

export const RegisterScreen: React.FC = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const { register, isLoading, error, clearError } = useAuthStore();
  const { currentLocation } = useLocationStore();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleRegister = async () => {
    // Validation
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Erreur',
        text2: 'Veuillez remplir votre nom et prénom',
      });
      return;
    }

    if (!formData.email.trim() || !formData.phone.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Erreur',
        text2: 'Veuillez remplir votre email et téléphone',
      });
      return;
    }

    if (formData.password.length < 6) {
      Toast.show({
        type: 'error',
        text1: 'Erreur',
        text2: 'Le mot de passe doit contenir au moins 6 caractères',
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Toast.show({
        type: 'error',
        text1: 'Erreur',
        text2: 'Les mots de passe ne correspondent pas',
      });
      return;
    }

    try {
      clearError();
      
      const registerData = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim(),
        password: formData.password,
        languages: ['fr'], // Default to French
        ...(currentLocation && {
          location: {
            coordinates: [currentLocation.longitude, currentLocation.latitude],
            address: currentLocation.address,
            city: currentLocation.city,
            country: 'Sénégal', // Default for now
          },
        }),
      };

      await register(registerData);
      
      Toast.show({
        type: 'success',
        text1: 'Inscription réussie',
        text2: 'Bienvenue sur Nowee !',
      });
    } catch (err) {
      Toast.show({
        type: 'error',
        text1: 'Erreur d\'inscription',
        text2: error || 'Veuillez réessayer',
      });
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (isLoading) {
    return <LoadingSpinner message="Création de votre compte..." />;
  }

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text variant="displaySmall" style={styles.title}>
            Rejoignez Nowee
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Créez votre compte et commencez à aider votre communauté
          </Text>
        </View>

        <Card style={styles.card}>
          <Card.Content style={styles.cardContent}>
            <Text variant="headlineSmall" style={styles.cardTitle}>
              Inscription
            </Text>

            <View style={styles.nameRow}>
              <TextInput
                label="Prénom"
                value={formData.firstName}
                onChangeText={(value) => updateFormData('firstName', value)}
                mode="outlined"
                style={[styles.input, styles.nameInput]}
                autoCapitalize="words"
                textContentType="givenName"
              />
              <TextInput
                label="Nom"
                value={formData.lastName}
                onChangeText={(value) => updateFormData('lastName', value)}
                mode="outlined"
                style={[styles.input, styles.nameInput]}
                autoCapitalize="words"
                textContentType="familyName"
              />
            </View>

            <TextInput
              label="Email"
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              textContentType="emailAddress"
            />

            <TextInput
              label="Téléphone"
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              mode="outlined"
              style={styles.input}
              keyboardType="phone-pad"
              placeholder="+221 77 123 45 67"
              textContentType="telephoneNumber"
            />

            <TextInput
              label="Mot de passe"
              value={formData.password}
              onChangeText={(value) => updateFormData('password', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              textContentType="newPassword"
            />

            <TextInput
              label="Confirmer le mot de passe"
              value={formData.confirmPassword}
              onChangeText={(value) => updateFormData('confirmPassword', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showConfirmPassword}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                />
              }
              textContentType="newPassword"
            />

            <Button
              mode="contained"
              onPress={handleRegister}
              style={styles.registerButton}
              loading={isLoading}
              disabled={isLoading}
            >
              S'inscrire
            </Button>

            <View style={styles.loginContainer}>
              <Text variant="bodyMedium" style={styles.loginText}>
                Déjà un compte ?
              </Text>
              <Button
                mode="text"
                onPress={navigateToLogin}
                style={styles.loginButton}
              >
                Se connecter
              </Button>
            </View>
          </Card.Content>
        </Card>

        <Text variant="bodySmall" style={styles.footer}>
          En vous inscrivant, vous acceptez nos conditions d'utilisation et notre politique de confidentialité
        </Text>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    color: colors.white,
    fontWeight: 'bold',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  card: {
    marginBottom: spacing.lg,
  },
  cardContent: {
    padding: spacing.xl,
  },
  cardTitle: {
    textAlign: 'center',
    marginBottom: spacing.xl,
    color: colors.text,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  input: {
    marginBottom: spacing.md,
  },
  registerButton: {
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    color: colors.textSecondary,
  },
  loginButton: {
    marginLeft: spacing.sm,
  },
  footer: {
    textAlign: 'center',
    color: colors.white,
    opacity: 0.8,
    lineHeight: 20,
  },
});
