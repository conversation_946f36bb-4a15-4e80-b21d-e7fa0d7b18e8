import { Server, Socket } from 'socket.io';
import { IUser } from '@/models/User';
import { logger } from '@/utils/logger';

interface AuthenticatedSocket extends Socket {
  user?: IUser;
  userId?: string;
}

export const chatHandlers = (io: Server, socket: AuthenticatedSocket) => {
  // Join conversation room
  socket.on('chat:join', (data: { conversationId: string }) => {
    try {
      socket.join(`conversation:${data.conversationId}`);
      logger.info(`User ${socket.userId} joined conversation ${data.conversationId}`);
    } catch (error) {
      logger.error('Erreur join conversation:', error);
      socket.emit('error', { message: 'Erreur lors de la connexion à la conversation' });
    }
  });

  // Leave conversation room
  socket.on('chat:leave', (data: { conversationId: string }) => {
    try {
      socket.leave(`conversation:${data.conversationId}`);
      logger.info(`User ${socket.userId} left conversation ${data.conversationId}`);
    } catch (error) {
      logger.error('Erreur leave conversation:', error);
    }
  });

  // Handle new message
  socket.on('chat:message', (data: {
    conversationId: string;
    content: string;
    type: 'text' | 'image' | 'audio' | 'location';
    metadata?: any;
  }) => {
    try {
      // Broadcast message to conversation participants
      socket.to(`conversation:${data.conversationId}`).emit('chat:new_message', {
        ...data,
        senderId: socket.userId,
        sender: socket.user?.toPublicJSON(),
        timestamp: new Date(),
      });

      logger.info(`Message sent in conversation ${data.conversationId} by user ${socket.userId}`);
    } catch (error) {
      logger.error('Erreur envoi message:', error);
      socket.emit('error', { message: 'Erreur lors de l\'envoi du message' });
    }
  });

  // Handle message read
  socket.on('chat:read', (data: { conversationId: string; messageId: string }) => {
    try {
      socket.to(`conversation:${data.conversationId}`).emit('chat:message_read', {
        messageId: data.messageId,
        readBy: socket.userId,
        readAt: new Date(),
      });
    } catch (error) {
      logger.error('Erreur lecture message:', error);
    }
  });

  // Handle voice call initiation
  socket.on('call:initiate', (data: { conversationId: string; type: 'audio' | 'video' }) => {
    try {
      socket.to(`conversation:${data.conversationId}`).emit('call:incoming', {
        callerId: socket.userId,
        caller: socket.user?.toPublicJSON(),
        type: data.type,
        conversationId: data.conversationId,
      });
    } catch (error) {
      logger.error('Erreur initiation appel:', error);
    }
  });

  // Handle call response
  socket.on('call:response', (data: { 
    conversationId: string; 
    accepted: boolean; 
    callerId: string;
  }) => {
    try {
      socket.to(`user:${data.callerId}`).emit('call:response', {
        accepted: data.accepted,
        responderId: socket.userId,
        responder: socket.user?.toPublicJSON(),
      });
    } catch (error) {
      logger.error('Erreur réponse appel:', error);
    }
  });

  // Handle call end
  socket.on('call:end', (data: { conversationId: string }) => {
    try {
      socket.to(`conversation:${data.conversationId}`).emit('call:ended', {
        endedBy: socket.userId,
        endedAt: new Date(),
      });
    } catch (error) {
      logger.error('Erreur fin appel:', error);
    }
  });
};
