import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface ILocation {
  type: 'Point';
  coordinates: [number, number]; // [longitude, latitude]
  address?: string;
  city?: string;
  country?: string;
}

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  email: string;
  phone: string;
  password: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  bio?: string;
  languages: string[];
  location?: ILocation;
  isVerified: boolean;
  isActive: boolean;
  reputation: number;
  nowcoins: number;
  totalHelpsGiven: number;
  totalHelpsReceived: number;
  preferences: {
    notifications: {
      push: boolean;
      email: boolean;
      sms: boolean;
    };
    privacy: {
      showLocation: boolean;
      showPhone: boolean;
      showEmail: boolean;
    };
    searchRadius: number; // in kilometers
  };
  lastSeen: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateAuthToken(): string;
  toPublicJSON(): object;
}

const LocationSchema = new Schema<ILocation>({
  type: {
    type: String,
    enum: ['Point'],
    default: 'Point',
  },
  coordinates: {
    type: [Number],
    required: true,
    validate: {
      validator: function(coordinates: number[]) {
        return coordinates.length === 2 &&
               coordinates[0] >= -180 && coordinates[0] <= 180 && // longitude
               coordinates[1] >= -90 && coordinates[1] <= 90;     // latitude
      },
      message: 'Coordonnées invalides',
    },
  },
  address: String,
  city: String,
  country: String,
});

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email invalide'],
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    match: [/^\+[1-9]\d{1,14}$/, 'Numéro de téléphone invalide'],
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
    select: false, // Don't include password in queries by default
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  avatar: {
    type: String,
    default: null,
  },
  bio: {
    type: String,
    maxlength: 500,
  },
  languages: {
    type: [String],
    default: ['fr'],
    enum: ['fr', 'wo', 'ff', 'en'], // français, wolof, pulaar, anglais
  },
  location: {
    type: LocationSchema,
    index: '2dsphere',
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  reputation: {
    type: Number,
    default: 0,
    min: 0,
    max: 100,
  },
  nowcoins: {
    type: Number,
    default: parseInt(process.env.NOWCOIN_INITIAL_BALANCE || '100'),
    min: 0,
  },
  totalHelpsGiven: {
    type: Number,
    default: 0,
    min: 0,
  },
  totalHelpsReceived: {
    type: Number,
    default: 0,
    min: 0,
  },
  preferences: {
    notifications: {
      push: { type: Boolean, default: true },
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: false },
    },
    privacy: {
      showLocation: { type: Boolean, default: true },
      showPhone: { type: Boolean, default: false },
      showEmail: { type: Boolean, default: false },
    },
    searchRadius: {
      type: Number,
      default: parseInt(process.env.DEFAULT_SEARCH_RADIUS_KM || '5'),
      min: 1,
      max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50'),
    },
  },
  lastSeen: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ phone: 1 });
UserSchema.index({ location: '2dsphere' });
UserSchema.index({ isActive: 1, isVerified: 1 });
UserSchema.index({ reputation: -1 });
UserSchema.index({ lastSeen: -1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS || '12'));
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Method to compare password
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to generate public JSON (without sensitive data)
UserSchema.methods.toPublicJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.__v;
  
  // Hide sensitive info based on privacy settings
  if (!user.preferences.privacy.showEmail) {
    delete user.email;
  }
  if (!user.preferences.privacy.showPhone) {
    delete user.phone;
  }
  if (!user.preferences.privacy.showLocation) {
    delete user.location;
  }
  
  return user;
};

export const User = mongoose.model<IUser>('User', UserSchema);
