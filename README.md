# 🤝 Nowee - L'Entraide Locale Intelligente

<div align="center">

![Nowee Logo](https://via.placeholder.com/200x200/667eea/ffffff?text=NOWEE)

**Connecter les communautés, faciliter l'entraide, créer du lien social**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/nowee-app/nowee)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![React Native](https://img.shields.io/badge/React%20Native-0.72-blue.svg)](https://reactnative.dev/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)

[🚀 Démo Live](https://demo.nowee.app) • [📱 Télécharger](https://apps.nowee.app) • [📚 Documentation](https://docs.nowee.app) • [🤝 Contribuer](docs/contributing.md)

</div>

## 🌍 À Propos de Nowee

Nowee est une plateforme d'entraide locale qui utilise l'intelligence artificielle pour connecter les membres d'une communauté et faciliter l'échange de services, d'objets et de compétences. Conçue spécialement pour l'Afrique de l'Ouest, elle prend en compte les spécificités culturelles et linguistiques de la région.

### 🎯 Mission
Renforcer les liens communautaires en facilitant l'entraide de proximité grâce à la technologie, tout en préservant les valeurs traditionnelles de solidarité africaine.

### ✨ Fonctionnalités Principales

- 🤖 **IA Conversationnelle** : Compréhension naturelle des besoins en français, wolof, pulaar et anglais
- 📍 **Géolocalisation Intelligente** : Matching automatique basé sur la proximité
- 💰 **NowCoins** : Système de monnaie locale pour valoriser l'entraide
- 🔄 **Échanges Flexibles** : Services, objets, compétences ou aide gratuite
- 💬 **Chat Intégré** : Communication directe entre utilisateurs
- 🌐 **Multilingue** : Support natif des langues locales
- 📱 **Mobile First** : Application native iOS et Android

## 🚀 Démarrage Rapide

### Prérequis
```bash
node --version    # v18.0.0+
npm --version     # v9.0.0+
git --version     # v2.0.0+
docker --version  # v20.0.0+
```

### Installation

1. **Cloner le repository**
```bash
git clone https://github.com/nowee-app/nowee.git
cd nowee
```

2. **Installer toutes les dépendances**
```bash
npm run install:all
```

3. **Configuration**
```bash
# Copier les fichiers d'environnement
cp backend/.env.example backend/.env
cp mobile/.env.example mobile/.env

# Éditer les variables d'environnement
nano backend/.env
nano mobile/.env
```

4. **Démarrer les services**
```bash
# Base de données et services (Docker)
npm run docker:up

# Backend API et Mobile App
npm run dev
```

## 🏗️ Architecture

### Vue d'ensemble
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Backend API   │    │    Database     │
│  (React Native) │◄──►│   (Node.js)     │◄──►│   (MongoDB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   AI Services   │              │
         │              │   (OpenAI)      │              │
         │              └─────────────────┘              │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Push Notifs   │    │   File Storage  │    │   Geolocation   │
│   (Firebase)    │    │      (AWS S3)   │    │  (Google Maps)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Stack Technique

**Frontend Mobile**
- React Native 0.72
- TypeScript
- Zustand (State Management)
- React Navigation 6
- React Native Maps
- React Native Voice

**Backend API**
- Node.js 18+
- Express.js
- TypeScript
- MongoDB avec Mongoose
- Redis (Cache)
- JWT Authentication
- Socket.io (Real-time)

**Services Externes**
- OpenAI GPT-4 (IA)
- Google Maps (Géolocalisation)
- Twilio (SMS)
- AWS S3 (Stockage)
- Firebase (Notifications)

## 🛠️ Développement

### Structure du Projet
```
nowee/
├── backend/                 # API Node.js
│   ├── src/
│   │   ├── controllers/     # Contrôleurs API
│   │   ├── models/          # Modèles de données
│   │   ├── services/        # Services métier
│   │   ├── middleware/      # Middlewares Express
│   │   └── utils/           # Utilitaires
│   ├── tests/               # Tests backend
│   └── package.json
├── mobile/                  # App React Native
│   ├── src/
│   │   ├── screens/         # Écrans de l'app
│   │   ├── components/      # Composants réutilisables
│   │   ├── services/        # Services API
│   │   ├── stores/          # État global
│   │   └── types/           # Types TypeScript
│   ├── android/             # Code Android natif
│   ├── ios/                 # Code iOS natif
│   └── package.json
├── docs/                    # Documentation
├── design/                  # Maquettes et design
└── docker-compose.yml       # Services de développement
```

### Scripts Disponibles

**Projet Global**
```bash
npm run dev              # Démarrage complet (backend + mobile)
npm run install:all      # Installation de toutes les dépendances
npm run test             # Tests complets
npm run lint             # Linting complet
npm run docker:up        # Démarrage des services Docker
```

**Backend**
```bash
npm run backend:dev      # Démarrage backend seul
npm run backend:build    # Build de production
npm run backend:test     # Tests backend
```

**Mobile**
```bash
npm run mobile:start     # Démarrage Metro bundler
npm run mobile:ios       # Lancement sur iOS
npm run mobile:android   # Lancement sur Android
npm run mobile:test      # Tests mobile
```

## 📚 Documentation

- [📖 Documentation API](docs/api-documentation.md)
- [🚀 Guide de Déploiement](docs/deployment-guide.md)
- [🤝 Guide de Contribution](docs/contributing.md)
- [🎨 Design System](design/design-system.md)
- [📐 Spécifications Figma](design/figma-specifications.md)

## 🤝 Contribution

Nous accueillons chaleureusement les contributions ! Consultez notre [Guide de Contribution](docs/contributing.md) pour commencer.

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 📞 Contact et Support

- **Site Web** : [nowee.app](https://nowee.app)
- **Email** : <EMAIL>
- **Twitter** : [@NoweeApp](https://twitter.com/NoweeApp)
- **LinkedIn** : [Nowee](https://linkedin.com/company/nowee-app)
- **Discord** : [Communauté Nowee](https://discord.gg/nowee)

---

<div align="center">

**Fait avec ❤️ pour l'Afrique de l'Ouest**

[⭐ Star ce projet](https://github.com/nowee-app/nowee) si vous le trouvez utile !

</div>
