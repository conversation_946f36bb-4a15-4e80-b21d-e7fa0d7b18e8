#!/bin/bash

# Nowee Setup Script
# This script helps set up the development environment for Nowee

set -e

echo "🚀 Setting up Nowee development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm 9+"
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. Some features may not work."
        print_warning "Please install Docker from https://docker.com/"
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose is not installed. Database services may not work."
    fi
    
    print_success "Requirements check completed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install mobile dependencies
    print_status "Installing mobile dependencies..."
    cd mobile
    npm install
    cd ..
    
    print_success "Dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cp backend/.env.example backend/.env
        print_warning "Please edit backend/.env with your configuration"
    else
        print_warning "backend/.env already exists, skipping..."
    fi
    
    # Mobile environment
    if [ ! -f "mobile/.env" ]; then
        print_status "Creating mobile .env file..."
        cp mobile/.env.example mobile/.env
        print_warning "Please edit mobile/.env with your configuration"
    else
        print_warning "mobile/.env already exists, skipping..."
    fi
    
    print_success "Environment files created"
}

# Setup database
setup_database() {
    print_status "Setting up database services..."
    
    if command -v docker-compose &> /dev/null; then
        print_status "Starting MongoDB and Redis with Docker..."
        docker-compose up -d mongodb redis
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 10
        
        print_success "Database services started"
    else
        print_warning "Docker Compose not available. Please start MongoDB and Redis manually."
        print_warning "MongoDB: mongodb://localhost:27017/nowee"
        print_warning "Redis: redis://localhost:6379"
    fi
}

# Run initial tests
run_tests() {
    print_status "Running initial tests..."
    
    # Backend tests
    print_status "Running backend tests..."
    cd backend
    npm run test || print_warning "Backend tests failed"
    cd ..
    
    # Mobile tests
    print_status "Running mobile tests..."
    cd mobile
    npm run test || print_warning "Mobile tests failed"
    cd ..
    
    print_success "Tests completed"
}

# Main setup function
main() {
    echo "🤝 Welcome to Nowee - L'Entraide Locale Intelligente"
    echo "=================================================="
    echo ""
    
    check_requirements
    echo ""
    
    install_dependencies
    echo ""
    
    setup_environment
    echo ""
    
    setup_database
    echo ""
    
    # Ask if user wants to run tests
    read -p "Do you want to run initial tests? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
        echo ""
    fi
    
    print_success "Setup completed successfully! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Edit backend/.env and mobile/.env with your configuration"
    echo "2. Start the development servers:"
    echo "   npm run dev"
    echo ""
    echo "3. For mobile development:"
    echo "   cd mobile"
    echo "   npm run android  # or npm run ios"
    echo ""
    echo "4. Access the services:"
    echo "   - Backend API: http://localhost:3000"
    echo "   - MongoDB Admin: http://localhost:8081"
    echo "   - Redis Admin: http://localhost:8082"
    echo ""
    echo "Happy coding! 🚀"
}

# Run main function
main "$@"
