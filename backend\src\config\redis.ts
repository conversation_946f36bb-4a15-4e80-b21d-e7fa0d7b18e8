import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';

let redisClient: RedisClientType;

export const connectRedis = async (): Promise<void> => {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    
    redisClient = createClient({
      url: redisUrl,
      password: process.env.REDIS_PASSWORD || undefined,
      socket: {
        reconnectStrategy: (retries) => Math.min(retries * 50, 1000),
      },
    });

    redisClient.on('error', (error) => {
      logger.error('❌ Erreur Redis:', error);
    });

    redisClient.on('connect', () => {
      logger.info('🔄 Connexion Redis en cours...');
    });

    redisClient.on('ready', () => {
      logger.info('✅ Redis prêt');
    });

    redisClient.on('end', () => {
      logger.warn('⚠️ Connexion Redis fermée');
    });

    await redisClient.connect();

    // Test de la connexion
    await redisClient.ping();
    logger.info('🏓 Redis ping réussi');

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await redisClient.quit();
      logger.info('🔌 Connexion Redis fermée via SIGINT');
    });

  } catch (error) {
    logger.error('❌ Erreur de connexion Redis:', error);
    throw error;
  }
};

export const getRedisClient = (): RedisClientType => {
  if (!redisClient) {
    throw new Error('Redis client n\'est pas initialisé');
  }
  return redisClient;
};

export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient) {
      await redisClient.quit();
      logger.info('🔌 Connexion Redis fermée');
    }
  } catch (error) {
    logger.error('❌ Erreur lors de la fermeture Redis:', error);
    throw error;
  }
};

// Utility functions for common Redis operations
export const redisUtils = {
  async set(key: string, value: string, expireInSeconds?: number): Promise<void> {
    const client = getRedisClient();
    if (expireInSeconds) {
      await client.setEx(key, expireInSeconds, value);
    } else {
      await client.set(key, value);
    }
  },

  async get(key: string): Promise<string | null> {
    const client = getRedisClient();
    return await client.get(key);
  },

  async del(key: string): Promise<number> {
    const client = getRedisClient();
    return await client.del(key);
  },

  async exists(key: string): Promise<boolean> {
    const client = getRedisClient();
    return (await client.exists(key)) === 1;
  },

  async setJson(key: string, value: object, expireInSeconds?: number): Promise<void> {
    await this.set(key, JSON.stringify(value), expireInSeconds);
  },

  async getJson<T>(key: string): Promise<T | null> {
    const value = await this.get(key);
    return value ? JSON.parse(value) : null;
  },

  async increment(key: string): Promise<number> {
    const client = getRedisClient();
    return await client.incr(key);
  },

  async expire(key: string, seconds: number): Promise<boolean> {
    const client = getRedisClient();
    return await client.expire(key, seconds);
  },
};
