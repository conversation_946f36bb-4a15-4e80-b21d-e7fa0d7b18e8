import express from 'express';
import { body, param, query } from 'express-validator';
import {
  createNeed,
  getNeeds,
  getNeedById,
  updateNeed,
  deleteNeed,
  searchNeeds,
  respondToNeed,
  acceptResponse,
  completeNeed,
  rateNeed,
  getUserNeeds
} from '@/controllers/needController';
import { authenticateToken, optionalAuth, requireVerification } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import { upload } from '@/middleware/upload';

const router = express.Router();

// Create need validation
const createNeedValidation = [
  body('title')
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Le titre doit contenir entre 5 et 100 caractères'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('La description doit contenir entre 10 et 1000 caractères'),
  body('category')
    .isIn(['transport', 'bricolage', 'menage', 'cuisine', 'garde', 'courses', 'informatique', 'education', 'sante', 'juridique', 'autre'])
    .withMessage('Catégorie invalide'),
  body('subcategory')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Sous-catégorie trop longue'),
  body('urgency')
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Urgence invalide'),
  body('type')
    .isIn(['service', 'object', 'skill', 'help'])
    .withMessage('Type invalide'),
  body('location.coordinates')
    .isArray({ min: 2, max: 2 })
    .withMessage('Coordonnées invalides')
    .custom((coordinates) => {
      const [lng, lat] = coordinates;
      return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
    })
    .withMessage('Coordonnées hors limites'),
  body('location.address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Adresse trop longue'),
  body('radius')
    .isInt({ min: 1, max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50') })
    .withMessage(`Rayon invalide (1-${process.env.MAX_SEARCH_RADIUS_KM || 50} km)`),
  body('compensation.type')
    .isIn(['free', 'nowcoins', 'money', 'exchange'])
    .withMessage('Type de compensation invalide'),
  body('compensation.amount')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Montant invalide'),
  body('compensation.description')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Description de compensation trop longue'),
  body('tags')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Maximum 10 tags autorisés'),
  body('expiresAt')
    .optional()
    .isISO8601()
    .withMessage('Date d\'expiration invalide')
    .custom((value) => {
      const expiresAt = new Date(value);
      const now = new Date();
      const maxExpiry = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days
      return expiresAt > now && expiresAt <= maxExpiry;
    })
    .withMessage('Date d\'expiration doit être future et dans les 30 jours'),
];

// Update need validation
const updateNeedValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 100 })
    .withMessage('Le titre doit contenir entre 5 et 100 caractères'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('La description doit contenir entre 10 et 1000 caractères'),
  body('urgency')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Urgence invalide'),
  body('compensation.type')
    .optional()
    .isIn(['free', 'nowcoins', 'money', 'exchange'])
    .withMessage('Type de compensation invalide'),
  body('compensation.amount')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Montant invalide'),
  body('tags')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Maximum 10 tags autorisés'),
];

// Search validation
const searchNeedsValidation = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Recherche trop courte'),
  query('category')
    .optional()
    .isIn(['transport', 'bricolage', 'menage', 'cuisine', 'garde', 'courses', 'informatique', 'education', 'sante', 'juridique', 'autre'])
    .withMessage('Catégorie invalide'),
  query('type')
    .optional()
    .isIn(['service', 'object', 'skill', 'help'])
    .withMessage('Type invalide'),
  query('urgency')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Urgence invalide'),
  query('compensation')
    .optional()
    .isIn(['free', 'nowcoins', 'money', 'exchange'])
    .withMessage('Type de compensation invalide'),
  query('lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude invalide'),
  query('lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude invalide'),
  query('radius')
    .optional()
    .isInt({ min: 1, max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50') })
    .withMessage('Rayon invalide'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limite invalide'),
];

// Response validation
const respondToNeedValidation = [
  body('message')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Le message doit contenir entre 10 et 500 caractères'),
  body('proposedCompensation')
    .optional()
    .isObject()
    .withMessage('Compensation proposée invalide'),
];

// Rating validation
const rateNeedValidation = [
  body('score')
    .isInt({ min: 1, max: 5 })
    .withMessage('Note invalide (1-5)'),
  body('comment')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Commentaire trop long'),
];

// ID validation
const needIdValidation = [
  param('id')
    .isMongoId()
    .withMessage('ID besoin invalide'),
];

// Routes

// CRUD operations
router.post('/', authenticateToken, requireVerification, upload.array('images', 5), createNeedValidation, validateRequest, createNeed);
router.get('/', optionalAuth, getNeeds);
router.get('/search', optionalAuth, searchNeedsValidation, validateRequest, searchNeeds);
router.get('/my-needs', authenticateToken, getUserNeeds);
router.get('/:id', optionalAuth, needIdValidation, validateRequest, getNeedById);
router.put('/:id', authenticateToken, needIdValidation, updateNeedValidation, validateRequest, updateNeed);
router.delete('/:id', authenticateToken, needIdValidation, validateRequest, deleteNeed);

// Need interactions
router.post('/:id/respond', authenticateToken, requireVerification, needIdValidation, respondToNeedValidation, validateRequest, respondToNeed);
router.post('/:id/accept/:responseId', authenticateToken, needIdValidation, validateRequest, acceptResponse);
router.post('/:id/complete', authenticateToken, needIdValidation, validateRequest, completeNeed);
router.post('/:id/rate', authenticateToken, needIdValidation, rateNeedValidation, validateRequest, rateNeed);

export default router;
