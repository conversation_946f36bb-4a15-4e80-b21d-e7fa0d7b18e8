{"name": "nowee", "version": "1.0.0", "description": "Plateforme d'entraide locale intelligente pour l'Afrique de l'Ouest", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run mobile:start\"", "backend:dev": "cd backend && npm run dev", "backend:build": "cd backend && npm run build", "backend:test": "cd backend && npm test", "mobile:start": "cd mobile && npm start", "mobile:ios": "cd mobile && npm run ios", "mobile:android": "cd mobile && npm run android", "mobile:test": "cd mobile && npm test", "install:all": "npm install && cd backend && npm install && cd ../mobile && npm install", "test": "npm run backend:test && npm run mobile:test", "lint": "cd backend && npm run lint && cd ../mobile && npm run lint", "format": "cd backend && npm run format && cd ../mobile && npm run format", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh", "clean": "npm run clean:backend && npm run clean:mobile", "clean:backend": "cd backend && rm -rf node_modules dist coverage", "clean:mobile": "cd mobile && rm -rf node_modules && npm run clean", "reset": "npm run clean && npm run install:all"}, "keywords": ["entraide", "communauté", "afrique", "react-native", "nodejs", "ia", "géolocalisation"], "author": "Nowee Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "mobile"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}