import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getProfile,
  updateProfile,
  uploadAvatar,
  deleteAvatar,
  getUserById,
  searchUsers,
  updateLocation,
  updatePreferences,
  deactivateAccount,
  getUserStats
} from '@/controllers/userController';
import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import { upload } from '@/middleware/upload';

const router = express.Router();

// Profile validation
const updateProfileValidation = [
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le prénom doit contenir entre 2 et 50 caractères'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Le nom doit contenir entre 2 et 50 caractères'),
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('La bio ne peut pas dépasser 500 caractères'),
  body('languages')
    .optional()
    .isArray()
    .withMessage('Les langues doivent être un tableau')
    .custom((languages) => {
      const validLanguages = ['fr', 'wo', 'ff', 'en'];
      return languages.every((lang: string) => validLanguages.includes(lang));
    })
    .withMessage('Langues invalides (fr, wo, ff, en autorisées)'),
];

// Location validation
const updateLocationValidation = [
  body('coordinates')
    .isArray({ min: 2, max: 2 })
    .withMessage('Coordonnées invalides')
    .custom((coordinates) => {
      const [lng, lat] = coordinates;
      return lng >= -180 && lng <= 180 && lat >= -90 && lat <= 90;
    })
    .withMessage('Coordonnées hors limites'),
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Adresse trop longue'),
  body('city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Ville trop longue'),
  body('country')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Pays trop long'),
];

// Preferences validation
const updatePreferencesValidation = [
  body('notifications.push')
    .optional()
    .isBoolean()
    .withMessage('Préférence push invalide'),
  body('notifications.email')
    .optional()
    .isBoolean()
    .withMessage('Préférence email invalide'),
  body('notifications.sms')
    .optional()
    .isBoolean()
    .withMessage('Préférence SMS invalide'),
  body('privacy.showLocation')
    .optional()
    .isBoolean()
    .withMessage('Préférence localisation invalide'),
  body('privacy.showPhone')
    .optional()
    .isBoolean()
    .withMessage('Préférence téléphone invalide'),
  body('privacy.showEmail')
    .optional()
    .isBoolean()
    .withMessage('Préférence email invalide'),
  body('searchRadius')
    .optional()
    .isInt({ min: 1, max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50') })
    .withMessage(`Rayon de recherche invalide (1-${process.env.MAX_SEARCH_RADIUS_KM || 50} km)`),
];

// Search validation
const searchUsersValidation = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 2 })
    .withMessage('Recherche trop courte (minimum 2 caractères)'),
  query('lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude invalide'),
  query('lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude invalide'),
  query('radius')
    .optional()
    .isInt({ min: 1, max: parseInt(process.env.MAX_SEARCH_RADIUS_KM || '50') })
    .withMessage('Rayon invalide'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limite invalide (1-50)'),
];

// ID validation
const userIdValidation = [
  param('id')
    .isMongoId()
    .withMessage('ID utilisateur invalide'),
];

// Routes

// Profile management
router.get('/profile', authenticateToken, getProfile);
router.put('/profile', authenticateToken, updateProfileValidation, validateRequest, updateProfile);
router.get('/stats', authenticateToken, getUserStats);

// Avatar management
router.post('/avatar', authenticateToken, upload.single('avatar'), uploadAvatar);
router.delete('/avatar', authenticateToken, deleteAvatar);

// Location
router.put('/location', authenticateToken, updateLocationValidation, validateRequest, updateLocation);

// Preferences
router.put('/preferences', authenticateToken, updatePreferencesValidation, validateRequest, updatePreferences);

// Account management
router.delete('/account', authenticateToken, deactivateAccount);

// Public routes
router.get('/search', optionalAuth, searchUsersValidation, validateRequest, searchUsers);
router.get('/:id', optionalAuth, userIdValidation, validateRequest, getUserById);

export default router;
