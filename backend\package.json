{"name": "nowee-backend", "version": "1.0.0", "description": "Backend API pour Nowee - Plateforme d'entraide locale", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "openai": "^4.20.1", "twilio": "^4.19.0", "aws-sdk": "^2.1498.0", "firebase-admin": "^11.11.1", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "geolib": "^3.3.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nowee", "api", "backend", "nodejs", "typescript", "express", "mongodb"], "author": "Nowee Team", "license": "MIT"}