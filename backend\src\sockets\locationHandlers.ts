import { Server, Socket } from 'socket.io';
import { IUser } from '@/models/User';
import { logger } from '@/utils/logger';

interface AuthenticatedSocket extends Socket {
  user?: IUser;
  userId?: string;
}

export const locationHandlers = (io: Server, socket: AuthenticatedSocket) => {
  // Handle location update
  socket.on('location:update', async (data: {
    coordinates: [number, number];
    address?: string;
    city?: string;
    accuracy?: number;
  }) => {
    try {
      if (!socket.user) return;

      // Validate coordinates
      const [lng, lat] = data.coordinates;
      if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        socket.emit('error', { message: 'Coordonnées invalides' });
        return;
      }

      // Update user location in database
      socket.user.location = {
        type: 'Point',
        coordinates: data.coordinates,
        address: data.address,
        city: data.city,
      };
      
      await socket.user.save();

      // Broadcast location update to relevant users (if privacy allows)
      if (socket.user.preferences.privacy.showLocation) {
        socket.broadcast.emit('location:user_update', {
          userId: socket.userId,
          location: {
            coordinates: data.coordinates,
            city: data.city,
            accuracy: data.accuracy,
          },
          updatedAt: new Date(),
        });
      }

      socket.emit('location:updated', {
        message: 'Localisation mise à jour',
        location: socket.user.location,
      });

      logger.info(`Location updated for user ${socket.userId}`);
    } catch (error) {
      logger.error('Erreur mise à jour localisation:', error);
      socket.emit('error', { message: 'Erreur lors de la mise à jour de la localisation' });
    }
  });

  // Handle location sharing request
  socket.on('location:share_request', (data: {
    targetUserId: string;
    duration?: number; // in minutes
    message?: string;
  }) => {
    try {
      socket.to(`user:${data.targetUserId}`).emit('location:share_request', {
        requesterId: socket.userId,
        requester: socket.user?.toPublicJSON(),
        duration: data.duration || 60, // Default 1 hour
        message: data.message,
        requestedAt: new Date(),
      });

      logger.info(`Location share requested by ${socket.userId} to ${data.targetUserId}`);
    } catch (error) {
      logger.error('Erreur demande partage localisation:', error);
    }
  });

  // Handle location sharing response
  socket.on('location:share_response', (data: {
    requesterId: string;
    accepted: boolean;
    duration?: number;
  }) => {
    try {
      if (data.accepted && socket.user?.preferences.privacy.showLocation) {
        // Share location with requester
        socket.to(`user:${data.requesterId}`).emit('location:shared', {
          sharedBy: socket.userId,
          sharer: socket.user?.toPublicJSON(),
          location: socket.user.location,
          duration: data.duration || 60,
          sharedAt: new Date(),
        });

        // Set up automatic stop sharing after duration
        if (data.duration) {
          setTimeout(() => {
            socket.to(`user:${data.requesterId}`).emit('location:share_stopped', {
              sharedBy: socket.userId,
              reason: 'expired',
            });
          }, data.duration * 60 * 1000);
        }
      } else {
        socket.to(`user:${data.requesterId}`).emit('location:share_denied', {
          deniedBy: socket.userId,
          deniedAt: new Date(),
        });
      }

      logger.info(`Location share ${data.accepted ? 'accepted' : 'denied'} by ${socket.userId}`);
    } catch (error) {
      logger.error('Erreur réponse partage localisation:', error);
    }
  });

  // Handle stop location sharing
  socket.on('location:stop_sharing', (data: { targetUserId: string }) => {
    try {
      socket.to(`user:${data.targetUserId}`).emit('location:share_stopped', {
        sharedBy: socket.userId,
        reason: 'manual',
        stoppedAt: new Date(),
      });

      logger.info(`Location sharing stopped by ${socket.userId} for ${data.targetUserId}`);
    } catch (error) {
      logger.error('Erreur arrêt partage localisation:', error);
    }
  });

  // Handle proximity alert setup
  socket.on('location:proximity_alert', (data: {
    targetLocation: [number, number];
    radius: number; // in meters
    message?: string;
  }) => {
    try {
      // Store proximity alert (in a real app, you'd save this to database)
      socket.emit('location:proximity_alert_set', {
        alertId: `alert_${Date.now()}`,
        targetLocation: data.targetLocation,
        radius: data.radius,
        message: data.message,
        setAt: new Date(),
      });

      logger.info(`Proximity alert set by user ${socket.userId}`);
    } catch (error) {
      logger.error('Erreur alerte proximité:', error);
    }
  });
};
