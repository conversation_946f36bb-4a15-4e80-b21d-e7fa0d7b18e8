import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getConversations,
  getConversation,
  sendMessage,
  markAsRead,
  deleteConversation
} from '@/controllers/chatController';
import { authenticateToken } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';
import { upload } from '@/middleware/upload';

const router = express.Router();

// Send message validation
const sendMessageValidation = [
  body('content')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Le message doit contenir entre 1 et 1000 caractères'),
  body('type')
    .isIn(['text', 'image', 'audio', 'location'])
    .withMessage('Type de message invalide'),
  body('location')
    .optional()
    .isObject()
    .withMessage('Localisation invalide'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limite invalide'),
];

// ID validation
const conversationIdValidation = [
  param('conversationId')
    .isMongoId()
    .withMessage('ID conversation invalide'),
];

const userIdValidation = [
  param('userId')
    .isMongoId()
    .withMessage('ID utilisateur invalide'),
];

// Routes
router.get('/', authenticateToken, paginationValidation, validateRequest, getConversations);
router.get('/:conversationId', authenticateToken, conversationIdValidation, paginationValidation, validateRequest, getConversation);
router.post('/:userId/message', authenticateToken, userIdValidation, upload.single('file'), sendMessageValidation, validateRequest, sendMessage);
router.put('/:conversationId/read', authenticateToken, conversationIdValidation, validateRequest, markAsRead);
router.delete('/:conversationId', authenticateToken, conversationIdValidation, validateRequest, deleteConversation);

export default router;
