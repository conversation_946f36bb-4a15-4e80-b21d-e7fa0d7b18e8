import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, Button, FAB, Searchbar } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useAuthStore } from '@/stores/authStore';
import { useLocationStore } from '@/stores/locationStore';
import { RootStackParamList } from '@/navigation/AppNavigator';
import { colors, spacing, categoryColors } from '@/constants/theme';
import { Need } from '@/types/need';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const CATEGORIES = [
  { id: 'transport', name: 'Transport', icon: 'car' },
  { id: 'bricolage', name: 'Bricolage', icon: 'hammer' },
  { id: 'menage', name: '<PERSON><PERSON><PERSON>', icon: 'home' },
  { id: 'cuisine', name: '<PERSON><PERSON><PERSON><PERSON>', icon: 'chef-hat' },
  { id: 'garde', name: '<PERSON><PERSON><PERSON>', icon: 'baby-face' },
  { id: 'courses', name: 'Courses', icon: 'cart' },
];

export const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuthStore();
  const { currentLocation } = useLocationStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [recentNeeds, setRecentNeeds] = useState<Need[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadRecentNeeds();
  }, []);

  const loadRecentNeeds = async () => {
    try {
      // TODO: Implement API call to fetch recent needs
      setRecentNeeds([]);
    } catch (error) {
      console.error('Error loading recent needs:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadRecentNeeds();
    setIsRefreshing(false);
  };

  const navigateToCreateNeed = () => {
    navigation.navigate('CreateNeed');
  };

  const navigateToNeeds = () => {
    navigation.navigate('MainTabs', { screen: 'Needs' });
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.greeting}>
            {getGreeting()}, {user?.firstName} !
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            Comment pouvons-nous vous aider aujourd'hui ?
          </Text>
        </View>

        {/* Search Bar */}
        <Searchbar
          placeholder="Rechercher de l'aide..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          onSubmitEditing={() => {
            // TODO: Navigate to search results
          }}
        />

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Icon name="coin" size={24} color={colors.nowcoin} />
              <Text variant="titleMedium" style={styles.statValue}>
                {user?.nowcoins || 0}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                NowCoins
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Icon name="heart" size={24} color={colors.reputation} />
              <Text variant="titleMedium" style={styles.statValue}>
                {user?.reputation || 0}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Réputation
              </Text>
            </Card.Content>
          </Card>

          <Card style={styles.statCard}>
            <Card.Content style={styles.statContent}>
              <Icon name="hand-heart" size={24} color={colors.success} />
              <Text variant="titleMedium" style={styles.statValue}>
                {user?.totalHelpsGiven || 0}
              </Text>
              <Text variant="bodySmall" style={styles.statLabel}>
                Aides données
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Catégories populaires
          </Text>
          <View style={styles.categoriesGrid}>
            {CATEGORIES.map((category) => (
              <Card key={category.id} style={styles.categoryCard}>
                <Card.Content style={styles.categoryContent}>
                  <Icon
                    name={category.icon}
                    size={32}
                    color={categoryColors[category.id as keyof typeof categoryColors]}
                  />
                  <Text variant="bodyMedium" style={styles.categoryName}>
                    {category.name}
                  </Text>
                </Card.Content>
              </Card>
            ))}
          </View>
        </View>

        {/* Recent Needs */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Besoins récents
            </Text>
            <Button mode="text" onPress={navigateToNeeds}>
              Voir tout
            </Button>
          </View>

          {recentNeeds.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Card.Content style={styles.emptyContent}>
                <Icon name="hand-heart-outline" size={48} color={colors.gray400} />
                <Text variant="bodyLarge" style={styles.emptyTitle}>
                  Aucun besoin récent
                </Text>
                <Text variant="bodyMedium" style={styles.emptySubtitle}>
                  Soyez le premier à demander de l'aide dans votre quartier !
                </Text>
                <Button mode="contained" onPress={navigateToCreateNeed} style={styles.emptyButton}>
                  Créer un besoin
                </Button>
              </Card.Content>
            </Card>
          ) : (
            recentNeeds.map((need) => (
              <Card key={need.id} style={styles.needCard}>
                <Card.Content>
                  <Text variant="titleMedium">{need.title}</Text>
                  <Text variant="bodyMedium" numberOfLines={2}>
                    {need.description}
                  </Text>
                </Card.Content>
              </Card>
            ))
          )}
        </View>
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={navigateToCreateNeed}
        label="Demander de l'aide"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: spacing.lg,
    backgroundColor: colors.primary,
  },
  greeting: {
    color: colors.white,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  subtitle: {
    color: colors.white,
    opacity: 0.9,
  },
  searchBar: {
    margin: spacing.lg,
    marginTop: -spacing.md,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  statCard: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: spacing.md,
  },
  statValue: {
    fontWeight: 'bold',
    marginTop: spacing.xs,
  },
  statLabel: {
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  section: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: colors.text,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: '48%',
    marginBottom: spacing.md,
  },
  categoryContent: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  categoryName: {
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  needCard: {
    marginBottom: spacing.md,
  },
  emptyCard: {
    marginTop: spacing.md,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    marginTop: spacing.md,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  emptySubtitle: {
    marginTop: spacing.sm,
    textAlign: 'center',
    color: colors.textSecondary,
  },
  emptyButton: {
    marginTop: spacing.lg,
  },
  fab: {
    position: 'absolute',
    margin: spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: colors.primary,
  },
});
