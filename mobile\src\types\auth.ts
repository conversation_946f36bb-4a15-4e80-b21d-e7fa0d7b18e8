export interface User {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  fullName: string;
  avatar?: string;
  bio?: string;
  languages: string[];
  location?: Location;
  isVerified: boolean;
  isActive: boolean;
  reputation: number;
  nowcoins: number;
  totalHelpsGiven: number;
  totalHelpsReceived: number;
  preferences: UserPreferences;
  lastSeen: string;
  createdAt: string;
  updatedAt: string;
}

export interface Location {
  type: 'Point';
  coordinates: [number, number]; // [longitude, latitude]
  address?: string;
  city?: string;
  country?: string;
}

export interface UserPreferences {
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
  };
  privacy: {
    showLocation: boolean;
    showPhone: boolean;
    showEmail: boolean;
  };
  searchRadius: number;
}

export interface LoginCredentials {
  identifier: string; // email or phone
  password: string;
}

export interface RegisterData {
  email: string;
  phone: string;
  password: string;
  firstName: string;
  lastName: string;
  languages?: string[];
  location?: {
    coordinates: [number, number];
    address?: string;
    city?: string;
    country?: string;
  };
}

export interface AuthResponse {
  success: boolean;
  user: User;
  token: string;
  refreshToken?: string;
  message?: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  token: string;
  user: User;
}

export interface VerificationData {
  token?: string;
  code?: string;
}

export interface ForgotPasswordData {
  identifier: string; // email or phone
}

export interface ResetPasswordData {
  token: string;
  password: string;
}
