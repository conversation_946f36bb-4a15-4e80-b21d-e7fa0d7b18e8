import React, { useEffect } from 'react';
import { StatusBar, LogBox } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { Provider as PaperProvider } from 'react-native-paper';

import { AppNavigator } from '@/navigation/AppNavigator';
import { useAuthStore } from '@/stores/authStore';
import { useLocationStore } from '@/stores/locationStore';
import { useSocketStore } from '@/stores/socketStore';
import { theme } from '@/constants/theme';
import { initializeI18n } from '@/utils/i18n';
import { initializeNotifications } from '@/services/notificationService';
import { LoadingSpinner } from '@/components/common/LoadingSpinner';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';

// Ignore specific warnings in development
if (__DEV__) {
  LogBox.ignoreLogs([
    'VirtualizedLists should never be nested',
    'Setting a timer for a long period of time',
  ]);
}

const App: React.FC = () => {
  const { isLoading, initialize: initializeAuth } = useAuthStore();
  const { initialize: initializeLocation } = useLocationStore();
  const { initialize: initializeSocket } = useSocketStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize i18n
        await initializeI18n();
        
        // Initialize auth (check for stored token)
        await initializeAuth();
        
        // Initialize location services
        await initializeLocation();
        
        // Initialize notifications
        await initializeNotifications();
        
        // Initialize socket connection
        initializeSocket();
      } catch (error) {
        console.error('Error initializing app:', error);
      }
    };

    initializeApp();
  }, [initializeAuth, initializeLocation, initializeSocket]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <PaperProvider theme={theme}>
            <StatusBar
              barStyle="light-content"
              backgroundColor={theme.colors.primary}
              translucent={false}
            />
            <NavigationContainer>
              <AppNavigator />
            </NavigationContainer>
            <Toast />
          </PaperProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
};

export default App;
