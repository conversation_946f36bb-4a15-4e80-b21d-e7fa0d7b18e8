const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  resolver: {
    alias: {
      '@': './src',
      '@/components': './src/components',
      '@/screens': './src/screens',
      '@/services': './src/services',
      '@/stores': './src/stores',
      '@/types': './src/types',
      '@/utils': './src/utils',
      '@/hooks': './src/hooks',
      '@/constants': './src/constants',
      '@/assets': './assets',
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
