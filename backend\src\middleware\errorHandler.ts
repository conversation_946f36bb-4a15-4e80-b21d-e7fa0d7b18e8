import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let { statusCode = 500, message } = error;

  // Log error
  logger.error(`Error ${statusCode}: ${message}`, {
    error: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Mongoose validation error
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Données invalides';
  }

  // Mongoose duplicate key error
  if (error.name === 'MongoServerError' && (error as any).code === 11000) {
    statusCode = 409;
    const field = Object.keys((error as any).keyValue)[0];
    message = `${field} déjà utilisé`;
  }

  // Mongoose cast error (invalid ObjectId)
  if (error.name === 'CastError') {
    statusCode = 400;
    message = 'ID invalide';
  }

  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Token invalide';
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expiré';
  }

  // Multer errors (file upload)
  if (error.name === 'MulterError') {
    statusCode = 400;
    if ((error as any).code === 'LIMIT_FILE_SIZE') {
      message = 'Fichier trop volumineux';
    } else if ((error as any).code === 'LIMIT_FILE_COUNT') {
      message = 'Trop de fichiers';
    } else {
      message = 'Erreur de téléchargement de fichier';
    }
  }

  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production' && !error.isOperational) {
    message = 'Erreur interne du serveur';
  }

  res.status(statusCode).json({
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && {
      stack: error.stack,
      error: error,
    }),
  });
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new CustomError(`Route ${req.originalUrl} non trouvée`, 404);
  next(error);
};

// Validation error helper
export const validationError = (message: string): CustomError => {
  return new CustomError(message, 400);
};

// Unauthorized error helper
export const unauthorizedError = (message: string = 'Non autorisé'): CustomError => {
  return new CustomError(message, 401);
};

// Forbidden error helper
export const forbiddenError = (message: string = 'Accès interdit'): CustomError => {
  return new CustomError(message, 403);
};

// Not found error helper
export const notFoundError = (message: string = 'Ressource non trouvée'): CustomError => {
  return new CustomError(message, 404);
};

// Conflict error helper
export const conflictError = (message: string = 'Conflit'): CustomError => {
  return new CustomError(message, 409);
};

// Internal server error helper
export const internalServerError = (message: string = 'Erreur interne du serveur'): CustomError => {
  return new CustomError(message, 500);
};
