import { apiService } from './apiService';
import {
  User,
  LoginCredentials,
  RegisterData,
  AuthResponse,
  RefreshTokenResponse,
  VerificationData,
  ForgotPasswordData,
  ResetPasswordData,
} from '@/types/auth';

class AuthService {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/login', credentials);
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    return apiService.post<AuthResponse>('/auth/register', data);
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    }
  }

  async refreshToken(): Promise<RefreshTokenResponse> {
    return apiService.post<RefreshTokenResponse>('/auth/refresh-token');
  }

  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {
    return apiService.post('/auth/forgot-password', data);
  }

  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    return apiService.post('/auth/reset-password', data);
  }

  async verifyEmail(data: VerificationData): Promise<{ message: string }> {
    return apiService.post('/auth/verify-email', data);
  }

  async verifyPhone(data: VerificationData): Promise<{ message: string }> {
    return apiService.post('/auth/verify-phone', data);
  }

  async resendVerification(): Promise<{ message: string }> {
    return apiService.post('/auth/resend-verification');
  }

  async getProfile(): Promise<User> {
    return apiService.get<User>('/users/profile');
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    return apiService.put<User>('/users/profile', data);
  }

  async uploadAvatar(file: any): Promise<{ avatar: string; message: string }> {
    return apiService.uploadFile('/users/avatar', file, 'avatar');
  }

  async deleteAvatar(): Promise<{ message: string }> {
    return apiService.delete('/users/avatar');
  }

  async updateLocation(location: {
    coordinates: [number, number];
    address?: string;
    city?: string;
    country?: string;
  }): Promise<User> {
    return apiService.put<User>('/users/location', { location });
  }

  async updatePreferences(preferences: Partial<User['preferences']>): Promise<User> {
    return apiService.put<User>('/users/preferences', preferences);
  }

  async deactivateAccount(): Promise<{ message: string }> {
    return apiService.delete('/users/account');
  }

  async getUserStats(): Promise<{
    totalHelpsGiven: number;
    totalHelpsReceived: number;
    reputation: number;
    nowcoins: number;
    joinedDate: string;
    completionRate: number;
    averageRating: number;
  }> {
    return apiService.get('/users/stats');
  }

  async searchUsers(params: {
    q?: string;
    lat?: number;
    lng?: number;
    radius?: number;
    page?: number;
    limit?: number;
  }): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    return apiService.get('/users/search', { params });
  }

  async getUserById(id: string): Promise<User> {
    return apiService.get<User>(`/users/${id}`);
  }
}

export const authService = new AuthService();
