import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useAuthStore } from '@/stores/authStore';
import { colors } from '@/constants/theme';

// Auth Screens
import { LoginScreen } from '@/screens/auth/LoginScreen';
import { RegisterScreen } from '@/screens/auth/RegisterScreen';
import { ForgotPasswordScreen } from '@/screens/auth/ForgotPasswordScreen';

// Main Screens
import { HomeScreen } from '@/screens/main/HomeScreen';
import { NeedsScreen } from '@/screens/main/NeedsScreen';
import { ChatScreen } from '@/screens/main/ChatScreen';
import { ProfileScreen } from '@/screens/main/ProfileScreen';

// Detail Screens
import { NeedDetailScreen } from '@/screens/details/NeedDetailScreen';
import { CreateNeedScreen } from '@/screens/create/CreateNeedScreen';
import { ConversationScreen } from '@/screens/chat/ConversationScreen';

export type RootStackParamList = {
  // Auth
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  
  // Main
  MainTabs: undefined;
  
  // Details
  NeedDetail: { needId: string };
  CreateNeed: undefined;
  Conversation: { conversationId: string; userId?: string };
};

export type MainTabParamList = {
  Home: undefined;
  Needs: undefined;
  Chat: undefined;
  Profile: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Needs':
              iconName = focused ? 'hand-heart' : 'hand-heart-outline';
              break;
            case 'Chat':
              iconName = focused ? 'chat' : 'chat-outline';
              break;
            case 'Profile':
              iconName = focused ? 'account' : 'account-outline';
              break;
            default:
              iconName = 'circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.gray400,
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ tabBarLabel: 'Accueil' }}
      />
      <Tab.Screen 
        name="Needs" 
        component={NeedsScreen}
        options={{ tabBarLabel: 'Besoins' }}
      />
      <Tab.Screen 
        name="Chat" 
        component={ChatScreen}
        options={{ tabBarLabel: 'Messages' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profil' }}
      />
    </Tab.Navigator>
  );
};

export const AppNavigator = () => {
  const { isAuthenticated } = useAuthStore();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
        },
        headerTintColor: colors.white,
        headerTitleStyle: {
          fontWeight: '600',
        },
      }}
    >
      {!isAuthenticated ? (
        // Auth Stack
        <>
          <Stack.Screen 
            name="Login" 
            component={LoginScreen}
            options={{ 
              title: 'Connexion',
              headerShown: false 
            }}
          />
          <Stack.Screen 
            name="Register" 
            component={RegisterScreen}
            options={{ 
              title: 'Inscription',
              headerShown: false 
            }}
          />
          <Stack.Screen 
            name="ForgotPassword" 
            component={ForgotPasswordScreen}
            options={{ title: 'Mot de passe oublié' }}
          />
        </>
      ) : (
        // Main App Stack
        <>
          <Stack.Screen 
            name="MainTabs" 
            component={MainTabNavigator}
            options={{ headerShown: false }}
          />
          <Stack.Screen 
            name="NeedDetail" 
            component={NeedDetailScreen}
            options={{ title: 'Détail du besoin' }}
          />
          <Stack.Screen 
            name="CreateNeed" 
            component={CreateNeedScreen}
            options={{ title: 'Créer un besoin' }}
          />
          <Stack.Screen 
            name="Conversation" 
            component={ConversationScreen}
            options={{ title: 'Conversation' }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};
