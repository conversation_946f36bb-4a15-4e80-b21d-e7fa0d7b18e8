import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { colors, spacing } from '@/constants/theme';

export const ConversationScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text variant="headlineMedium">Conversation</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.lg,
    backgroundColor: colors.background,
  },
});
