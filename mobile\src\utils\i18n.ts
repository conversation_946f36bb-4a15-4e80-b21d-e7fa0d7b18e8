import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translations
import fr from '@/locales/fr.json';
import en from '@/locales/en.json';
import wo from '@/locales/wo.json';
import ff from '@/locales/ff.json';

const STORAGE_KEY = 'user_language';

const resources = {
  fr: { translation: fr },
  en: { translation: en },
  wo: { translation: wo },
  ff: { translation: ff },
};

const getDeviceLanguage = (): string => {
  const locales = getLocales();
  const deviceLanguage = locales[0]?.languageCode || 'fr';
  
  // Map device language to supported languages
  const supportedLanguages = ['fr', 'en', 'wo', 'ff'];
  return supportedLanguages.includes(deviceLanguage) ? deviceLanguage : 'fr';
};

const getStoredLanguage = async (): Promise<string> => {
  try {
    const storedLanguage = await AsyncStorage.getItem(STORAGE_KEY);
    return storedLanguage || getDeviceLanguage();
  } catch (error) {
    console.error('Failed to get stored language:', error);
    return getDeviceLanguage();
  }
};

export const initializeI18n = async (): Promise<void> => {
  const language = await getStoredLanguage();

  await i18n
    .use(initReactI18next)
    .init({
      resources,
      lng: language,
      fallbackLng: 'fr',
      debug: __DEV__,
      
      interpolation: {
        escapeValue: false,
      },
      
      react: {
        useSuspense: false,
      },
    });
};

export const changeLanguage = async (language: string): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, language);
    await i18n.changeLanguage(language);
  } catch (error) {
    console.error('Failed to change language:', error);
  }
};

export const getCurrentLanguage = (): string => {
  return i18n.language;
};

export const getSupportedLanguages = () => [
  { code: 'fr', name: 'Français', nativeName: 'Français' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'wo', name: 'Wolof', nativeName: 'Wolof' },
  { code: 'ff', name: 'Pulaar', nativeName: 'Pulaar' },
];

export default i18n;
