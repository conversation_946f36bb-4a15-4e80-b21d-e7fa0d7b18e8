// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the nowee database
db = db.getSiblingDB('nowee');

// Create a user for the application
db.createUser({
  user: 'nowee_user',
  pwd: 'nowee_password',
  roles: [
    {
      role: 'readWrite',
      db: 'nowee'
    }
  ]
});

// Create indexes for better performance
print('Creating indexes...');

// Users collection indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ phone: 1 }, { unique: true });
db.users.createIndex({ location: '2dsphere' });
db.users.createIndex({ isActive: 1, isVerified: 1 });
db.users.createIndex({ reputation: -1 });
db.users.createIndex({ lastSeen: -1 });

// Needs collection indexes
db.needs.createIndex({ location: '2dsphere' });
db.needs.createIndex({ user: 1, status: 1 });
db.needs.createIndex({ category: 1, type: 1 });
db.needs.createIndex({ urgency: 1, createdAt: -1 });
db.needs.createIndex({ status: 1, expiresAt: 1 });
db.needs.createIndex({ tags: 1 });
db.needs.createIndex({ 'compensation.type': 1 });
db.needs.createIndex({ status: 1, location: '2dsphere', category: 1 });

// Offers collection indexes
db.offers.createIndex({ location: '2dsphere' });
db.offers.createIndex({ user: 1, status: 1 });
db.offers.createIndex({ category: 1, type: 1 });
db.offers.createIndex({ status: 1, createdAt: -1 });
db.offers.createIndex({ tags: 1 });

// Conversations collection indexes
db.conversations.createIndex({ participants: 1 });
db.conversations.createIndex({ updatedAt: -1 });
db.conversations.createIndex({ type: 1 });

// Messages collection indexes
db.messages.createIndex({ conversation: 1, createdAt: -1 });
db.messages.createIndex({ sender: 1 });
db.messages.createIndex({ type: 1 });

// NowCoin transactions collection indexes
db.nowcointransactions.createIndex({ user: 1, createdAt: -1 });
db.nowcointransactions.createIndex({ type: 1 });
db.nowcointransactions.createIndex({ fromUser: 1 });
db.nowcointransactions.createIndex({ toUser: 1 });

// Notifications collection indexes
db.notifications.createIndex({ user: 1, createdAt: -1 });
db.notifications.createIndex({ isRead: 1 });
db.notifications.createIndex({ type: 1 });

print('Indexes created successfully!');

// Insert some sample data for development
print('Inserting sample data...');

// Sample categories data
db.categories.insertMany([
  { _id: 'transport', name: 'Transport', icon: 'car', color: '#3498db' },
  { _id: 'bricolage', name: 'Bricolage', icon: 'hammer', color: '#e67e22' },
  { _id: 'menage', name: 'Ménage', icon: 'home', color: '#2ecc71' },
  { _id: 'cuisine', name: 'Cuisine', icon: 'utensils', color: '#e74c3c' },
  { _id: 'garde', name: 'Garde d\'enfants', icon: 'baby', color: '#f39c12' },
  { _id: 'courses', name: 'Courses', icon: 'shopping-cart', color: '#9b59b6' },
  { _id: 'informatique', name: 'Informatique', icon: 'laptop', color: '#34495e' },
  { _id: 'education', name: 'Éducation', icon: 'graduation-cap', color: '#16a085' },
  { _id: 'sante', name: 'Santé', icon: 'heartbeat', color: '#c0392b' },
  { _id: 'juridique', name: 'Juridique', icon: 'balance-scale', color: '#8e44ad' },
  { _id: 'autre', name: 'Autre', icon: 'ellipsis-h', color: '#95a5a6' }
]);

print('Sample data inserted successfully!');
print('MongoDB initialization completed!');
