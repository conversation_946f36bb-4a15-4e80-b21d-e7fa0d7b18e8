version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: nowee-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: nowee
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - nowee-network

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: nowee-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nowee-network

  # MongoDB Express (Database Admin UI)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: nowee-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongodb
    networks:
      - nowee-network

  # Redis Commander (Redis Admin UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: nowee-redis-commander
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis123
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    depends_on:
      - redis
    networks:
      - nowee-network

  # Elasticsearch (for advanced search - optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: nowee-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - nowee-network
    profiles:
      - search

  # Kibana (Elasticsearch UI - optional)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: nowee-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - nowee-network
    profiles:
      - search

  # MinIO (S3-compatible storage for development)
  minio:
    image: minio/minio:latest
    container_name: nowee-minio
    restart: unless-stopped
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - nowee-network
    profiles:
      - storage

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: nowee-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - nowee-network
    profiles:
      - email

  # Nginx (Reverse proxy for production)
  nginx:
    image: nginx:alpine
    container_name: nowee-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - nowee-network
    profiles:
      - production

  # Backend API (for production)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nowee-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      MONGODB_URI: ****************************************************************
      REDIS_URL: redis://:redis123@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - nowee-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local

networks:
  nowee-network:
    driver: bridge
