import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { CustomError } from './errorHandler';

// Configure storage
const storage = multer.memoryStorage();

// File filter
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,audio/mpeg,audio/wav').split(',');
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new CustomError(`Type de fichier non autorisé: ${file.mimetype}`, 400));
  }
};

// Configure multer
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB default
    files: 5, // Maximum 5 files
  },
});

// Helper function to generate unique filename
export const generateFileName = (originalName: string): string => {
  const ext = path.extname(originalName);
  const name = path.basename(originalName, ext);
  return `${uuidv4()}-${Date.now()}${ext}`;
};

// Helper function to validate file type
export const isValidFileType = (mimetype: string): boolean => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,audio/mpeg,audio/wav').split(',');
  return allowedTypes.includes(mimetype);
};

// Helper function to validate file size
export const isValidFileSize = (size: number): boolean => {
  const maxSize = parseInt(process.env.MAX_FILE_SIZE || '5242880');
  return size <= maxSize;
};
