import { Server, Socket } from 'socket.io';
import { IUser } from '@/models/User';
import { logger } from '@/utils/logger';

interface AuthenticatedSocket extends Socket {
  user?: IUser;
  userId?: string;
}

export const needHandlers = (io: Server, socket: AuthenticatedSocket) => {
  // Subscribe to need updates in a specific area
  socket.on('needs:subscribe_area', (data: { 
    lat: number; 
    lng: number; 
    radius: number;
    categories?: string[];
  }) => {
    try {
      const roomName = `area:${data.lat.toFixed(3)}_${data.lng.toFixed(3)}_${data.radius}`;
      socket.join(roomName);
      
      logger.info(`User ${socket.userId} subscribed to area ${roomName}`);
      
      socket.emit('needs:subscribed', { 
        area: roomName,
        message: 'Abonné aux besoins de la zone'
      });
    } catch (error) {
      logger.error('Erreur abonnement zone:', error);
      socket.emit('error', { message: 'Erreur lors de l\'abonnement à la zone' });
    }
  });

  // Unsubscribe from area updates
  socket.on('needs:unsubscribe_area', (data: { 
    lat: number; 
    lng: number; 
    radius: number;
  }) => {
    try {
      const roomName = `area:${data.lat.toFixed(3)}_${data.lng.toFixed(3)}_${data.radius}`;
      socket.leave(roomName);
      
      logger.info(`User ${socket.userId} unsubscribed from area ${roomName}`);
    } catch (error) {
      logger.error('Erreur désabonnement zone:', error);
    }
  });

  // Handle new need creation notification
  socket.on('needs:created', (data: {
    needId: string;
    title: string;
    category: string;
    urgency: string;
    location: { coordinates: [number, number] };
    radius: number;
  }) => {
    try {
      // Notify users in the relevant area
      const [lng, lat] = data.location.coordinates;
      const areaRoom = `area:${lat.toFixed(3)}_${lng.toFixed(3)}_${data.radius}`;
      
      socket.to(areaRoom).emit('needs:new_need', {
        needId: data.needId,
        title: data.title,
        category: data.category,
        urgency: data.urgency,
        createdBy: socket.userId,
        creator: socket.user?.toPublicJSON(),
        location: data.location,
        createdAt: new Date(),
      });

      logger.info(`New need ${data.needId} broadcasted to area ${areaRoom}`);
    } catch (error) {
      logger.error('Erreur notification nouveau besoin:', error);
    }
  });

  // Handle need response notification
  socket.on('needs:response', (data: {
    needId: string;
    needOwnerId: string;
    message: string;
  }) => {
    try {
      // Notify the need owner
      socket.to(`user:${data.needOwnerId}`).emit('needs:new_response', {
        needId: data.needId,
        responderId: socket.userId,
        responder: socket.user?.toPublicJSON(),
        message: data.message,
        respondedAt: new Date(),
      });

      logger.info(`Need response sent for need ${data.needId} to user ${data.needOwnerId}`);
    } catch (error) {
      logger.error('Erreur notification réponse besoin:', error);
    }
  });

  // Handle need status update
  socket.on('needs:status_update', (data: {
    needId: string;
    status: string;
    matchedWith?: string;
  }) => {
    try {
      // Notify relevant users about status change
      if (data.matchedWith) {
        socket.to(`user:${data.matchedWith}`).emit('needs:status_changed', {
          needId: data.needId,
          status: data.status,
          updatedBy: socket.userId,
          updatedAt: new Date(),
        });
      }

      logger.info(`Need ${data.needId} status updated to ${data.status}`);
    } catch (error) {
      logger.error('Erreur mise à jour statut besoin:', error);
    }
  });

  // Handle urgent need alert
  socket.on('needs:urgent_alert', (data: {
    needId: string;
    title: string;
    location: { coordinates: [number, number] };
    radius: number;
  }) => {
    try {
      // Broadcast urgent alert to wider area
      const [lng, lat] = data.location.coordinates;
      const urgentRadius = Math.min(data.radius * 2, 20); // Max 20km for urgent alerts
      const areaRoom = `area:${lat.toFixed(3)}_${lng.toFixed(3)}_${urgentRadius}`;
      
      socket.to(areaRoom).emit('needs:urgent_alert', {
        needId: data.needId,
        title: data.title,
        alertedBy: socket.userId,
        alerter: socket.user?.toPublicJSON(),
        location: data.location,
        alertedAt: new Date(),
      });

      logger.info(`Urgent alert sent for need ${data.needId} to area ${areaRoom}`);
    } catch (error) {
      logger.error('Erreur alerte urgente:', error);
    }
  });
};
