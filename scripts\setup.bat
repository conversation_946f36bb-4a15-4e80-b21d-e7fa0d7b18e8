@echo off
echo 🚀 Setting up Nowee development environment...

echo.
echo 📋 Checking Node.js version...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo 📦 Installing root dependencies...
npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)

echo.
echo 📦 Installing backend dependencies...
cd backend
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo 📦 Installing mobile dependencies...
cd mobile
npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ Failed to install mobile dependencies
    pause
    exit /b 1
)
cd ..

echo.
echo ⚙️ Setting up environment files...
if not exist "backend\.env" (
    echo Creating backend .env file...
    copy "backend\.env.example" "backend\.env"
    echo ⚠️ Please edit backend\.env with your configuration
) else (
    echo backend\.env already exists, skipping...
)

if not exist "mobile\.env" (
    echo Creating mobile .env file...
    copy "mobile\.env.example" "mobile\.env"
    echo ⚠️ Please edit mobile\.env with your configuration
) else (
    echo mobile\.env already exists, skipping...
)

echo.
echo ✅ Setup completed successfully! 🎉
echo.
echo Next steps:
echo 1. Edit backend\.env and mobile\.env with your configuration
echo 2. Start the development servers:
echo    npm run dev
echo.
echo 3. For mobile development:
echo    cd mobile
echo    npm run android  # or npm run ios
echo.
echo Happy coding! 🚀
pause
