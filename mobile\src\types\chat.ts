import { User } from './auth';

export interface Message {
  id: string;
  conversation: string;
  sender: User | string;
  content?: string;
  type: MessageType;
  metadata?: MessageMetadata;
  readBy: MessageRead[];
  createdAt: string;
  updatedAt: string;
}

export type MessageType = 'text' | 'image' | 'audio' | 'location' | 'file' | 'system';

export interface MessageMetadata {
  // For image messages
  imageUrl?: string;
  imageWidth?: number;
  imageHeight?: number;
  
  // For audio messages
  audioUrl?: string;
  duration?: number;
  
  // For location messages
  latitude?: number;
  longitude?: number;
  address?: string;
  
  // For file messages
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  
  // For system messages
  systemType?: 'need_matched' | 'need_completed' | 'user_joined' | 'user_left';
  systemData?: any;
}

export interface MessageRead {
  user: User | string;
  readAt: string;
}

export interface Conversation {
  id: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  type: ConversationType;
  metadata?: ConversationMetadata;
  createdAt: string;
  updatedAt: string;
}

export type ConversationType = 'direct' | 'need_related' | 'group';

export interface ConversationMetadata {
  // For need-related conversations
  needId?: string;
  needTitle?: string;
  
  // For group conversations
  groupName?: string;
  groupAvatar?: string;
  
  // General
  isArchived?: boolean;
  isMuted?: boolean;
  muteUntil?: string;
}

export interface SendMessageData {
  content?: string;
  type: MessageType;
  metadata?: Partial<MessageMetadata>;
}

export interface ConversationListResponse {
  conversations: Conversation[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface MessageListResponse {
  messages: Message[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  user: User;
  isTyping: boolean;
}

export interface CallData {
  id: string;
  conversation: string;
  caller: User;
  callee: User;
  type: 'audio' | 'video';
  status: 'ringing' | 'accepted' | 'declined' | 'ended' | 'missed';
  startedAt?: string;
  endedAt?: string;
  duration?: number; // in seconds
}
