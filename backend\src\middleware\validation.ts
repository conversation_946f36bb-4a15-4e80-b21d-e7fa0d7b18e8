import { Request, Response, NextFunction } from 'express';
import { validationResult, ValidationError } from 'express-validator';
import { logger } from '@/utils/logger';

export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map((error: ValidationError) => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined,
    }));

    logger.warn('Validation failed:', {
      url: req.url,
      method: req.method,
      errors: errorMessages,
      body: req.body,
    });

    res.status(400).json({
      success: false,
      message: 'Données invalides',
      errors: errorMessages,
    });
    return;
  }

  next();
};
