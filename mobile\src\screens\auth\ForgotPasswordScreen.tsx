import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, TextInput, Button, Card } from 'react-native-paper';
import { colors, spacing } from '@/constants/theme';

export const ForgotPasswordScreen: React.FC = () => {
  const [identifier, setIdentifier] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async () => {
    setIsLoading(true);
    // TODO: Implement password reset
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="headlineSmall" style={styles.title}>
            Mot de passe oublié
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            Entrez votre email ou téléphone pour recevoir un lien de réinitialisation
          </Text>
          
          <TextInput
            label="Email ou téléphone"
            value={identifier}
            onChangeText={setIdentifier}
            mode="outlined"
            style={styles.input}
          />
          
          <Button
            mode="contained"
            onPress={handleResetPassword}
            loading={isLoading}
            style={styles.button}
          >
            Envoyer le lien
          </Button>
        </Card.Content>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.lg,
    backgroundColor: colors.background,
    justifyContent: 'center',
  },
  card: {
    padding: spacing.lg,
  },
  title: {
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: spacing.xl,
    color: colors.textSecondary,
  },
  input: {
    marginBottom: spacing.lg,
  },
  button: {
    marginTop: spacing.md,
  },
});
