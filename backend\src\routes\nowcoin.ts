import express from 'express';
import { body, param, query } from 'express-validator';
import {
  getBalance,
  getTransactions,
  transferNowcoins,
  getDailyBonus,
  getLeaderboard
} from '@/controllers/nowcoinController';
import { authenticateToken, optionalAuth } from '@/middleware/auth';
import { validateRequest } from '@/middleware/validation';

const router = express.Router();

// Transfer validation
const transferValidation = [
  body('recipientId')
    .isMongoId()
    .withMessage('ID destinataire invalide'),
  body('amount')
    .isInt({ min: 1 })
    .withMessage('Montant invalide (minimum 1 NowCoin)'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Description trop longue'),
];

// Pagination validation
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page invalide'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limite invalide'),
  query('type')
    .optional()
    .isIn(['earned', 'spent', 'transfer_in', 'transfer_out', 'bonus'])
    .withMessage('Type de transaction invalide'),
];

// Routes
router.get('/balance', authenticateToken, getBalance);
router.get('/transactions', authenticateToken, paginationValidation, validateRequest, getTransactions);
router.post('/transfer', authenticateToken, transferValidation, validateRequest, transferNowcoins);
router.post('/daily-bonus', authenticateToken, getDailyBonus);
router.get('/leaderboard', optionalAuth, getLeaderboard);

export default router;
