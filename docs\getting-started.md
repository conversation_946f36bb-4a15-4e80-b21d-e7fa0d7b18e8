# 🚀 Guide de Démarrage - Nowee

Bienvenue dans le guide de démarrage de Nowee ! Ce guide vous accompagnera pas à pas pour configurer votre environnement de développement et commencer à contribuer au projet.

## 📋 Table des Matières

1. [Prérequis](#prérequis)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Premier Lancement](#premier-lancement)
5. [Structure du Projet](#structure-du-projet)
6. [Développement](#développement)
7. [Tests](#tests)
8. [Dépannage](#dépannage)

## 🔧 Prérequis

### Outils Obligatoires

| Outil | Version Minimale | Installation |
|-------|------------------|--------------|
| **Node.js** | 18.0.0+ | [nodejs.org](https://nodejs.org/) |
| **npm** | 9.0.0+ | Inclus avec Node.js |
| **Git** | 2.0.0+ | [git-scm.com](https://git-scm.com/) |
| **Docker** | 20.0.0+ | [docker.com](https://docker.com/) |

### Outils pour le Mobile

| Outil | Plateforme | Installation |
|-------|------------|--------------|
| **React Native CLI** | Toutes | `npm install -g react-native-cli` |
| **Android Studio** | Android | [developer.android.com](https://developer.android.com/studio) |
| **Xcode** | iOS | App Store (macOS uniquement) |
| **Java JDK** | Android | Version 11 ou 17 |

### Vérification des Prérequis

```bash
# Vérifier les versions installées
node --version    # Doit afficher v18.0.0 ou plus
npm --version     # Doit afficher v9.0.0 ou plus
git --version     # Doit afficher v2.0.0 ou plus
docker --version  # Doit afficher v20.0.0 ou plus

# Vérifier React Native CLI
npx react-native --version
```

## 📦 Installation

### Option 1 : Installation Automatique (Recommandée)

```bash
# 1. Cloner le repository
git clone https://github.com/nowee-app/nowee.git
cd nowee

# 2. Lancer le script de configuration
npm run setup
```

Le script `setup` va automatiquement :
- ✅ Vérifier les prérequis
- ✅ Installer toutes les dépendances
- ✅ Créer les fichiers d'environnement
- ✅ Démarrer les services de base de données
- ✅ Exécuter les tests initiaux

### Option 2 : Installation Manuelle

```bash
# 1. Cloner le repository
git clone https://github.com/nowee-app/nowee.git
cd nowee

# 2. Installer les dépendances root
npm install

# 3. Installer les dépendances backend
cd backend
npm install
cd ..

# 4. Installer les dépendances mobile
cd mobile
npm install
cd ..

# 5. Ou utiliser le script d'installation groupée
npm run install:all
```

## ⚙️ Configuration

### 1. Variables d'Environnement Backend

Copiez et configurez le fichier d'environnement backend :

```bash
cp backend/.env.example backend/.env
```

Éditez `backend/.env` avec vos configurations :

```env
# Base de données
MONGODB_URI=mongodb://localhost:27017/nowee
REDIS_URL=redis://localhost:6379

# Authentification
JWT_SECRET=your-super-secret-jwt-key-256-bits-minimum
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_EXPIRES_IN=7d

# Services externes
OPENAI_API_KEY=sk-your-openai-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token

# Email (optionnel pour le développement)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Stockage de fichiers
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=nowee-dev-uploads

# Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug
```

### 2. Variables d'Environnement Mobile

Copiez et configurez le fichier d'environnement mobile :

```bash
cp mobile/.env.example mobile/.env
```

Éditez `mobile/.env` :

```env
# API
API_BASE_URL=http://localhost:3000/api
SOCKET_URL=http://localhost:3000

# Google Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_APP_ID=your-firebase-app-id
FIREBASE_API_KEY=your-firebase-api-key

# Configuration
NODE_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug
```

### 3. Services de Base de Données

Démarrez MongoDB et Redis avec Docker :

```bash
# Démarrer tous les services
npm run docker:up

# Ou démarrer seulement les services de base
docker-compose up -d mongodb redis

# Vérifier que les services sont actifs
docker-compose ps
```

## 🚀 Premier Lancement

### 1. Démarrer le Backend

```bash
# En mode développement avec rechargement automatique
npm run backend:dev

# Ou manuellement
cd backend
npm run dev
```

Le backend sera accessible sur `http://localhost:3000`

### 2. Démarrer l'Application Mobile

Dans un nouveau terminal :

```bash
# Démarrer Metro (serveur de développement React Native)
npm run mobile:start

# Dans un autre terminal, lancer sur Android
npm run mobile:android

# Ou sur iOS (macOS uniquement)
npm run mobile:ios
```

### 3. Démarrage Complet

Pour démarrer backend et mobile simultanément :

```bash
npm run dev
```

## 📁 Structure du Projet

```
nowee/
├── 📁 backend/                 # API Node.js + TypeScript
│   ├── 📁 src/
│   │   ├── 📁 controllers/     # Contrôleurs API
│   │   ├── 📁 models/          # Modèles MongoDB
│   │   ├── 📁 routes/          # Routes Express
│   │   ├── 📁 middleware/      # Middlewares
│   │   ├── 📁 services/        # Services métier
│   │   ├── 📁 utils/           # Utilitaires
│   │   └── 📁 sockets/         # Handlers Socket.io
│   ├── 📁 tests/               # Tests backend
│   └── 📄 package.json
│
├── 📁 mobile/                  # App React Native
│   ├── 📁 src/
│   │   ├── 📁 components/      # Composants réutilisables
│   │   ├── 📁 screens/         # Écrans de l'app
│   │   ├── 📁 navigation/      # Configuration navigation
│   │   ├── 📁 stores/          # Gestion d'état (Zustand)
│   │   ├── 📁 services/        # Services API
│   │   ├── 📁 types/           # Types TypeScript
│   │   ├── 📁 utils/           # Utilitaires
│   │   ├── 📁 hooks/           # Hooks personnalisés
│   │   ├── 📁 constants/       # Constantes (thème, etc.)
│   │   └── 📁 locales/         # Traductions
│   ├── 📁 android/             # Code natif Android
│   ├── 📁 ios/                 # Code natif iOS
│   └── 📄 package.json
│
├── 📁 docs/                    # Documentation
├── 📁 scripts/                 # Scripts utilitaires
├── 📄 docker-compose.yml       # Services Docker
├── 📄 package.json             # Configuration root
└── 📄 README.md
```

## 💻 Développement

### Scripts Disponibles

| Script | Description |
|--------|-------------|
| `npm run dev` | Démarrer backend + mobile |
| `npm run backend:dev` | Démarrer seulement le backend |
| `npm run mobile:start` | Démarrer Metro bundler |
| `npm run mobile:android` | Lancer sur Android |
| `npm run mobile:ios` | Lancer sur iOS |
| `npm test` | Exécuter tous les tests |
| `npm run lint` | Vérifier la qualité du code |
| `npm run format` | Formater le code |

### Workflow de Développement

1. **Créer une branche** pour votre fonctionnalité
   ```bash
   git checkout -b feature/ma-nouvelle-fonctionnalite
   ```

2. **Développer** en utilisant les outils de développement
   ```bash
   npm run dev  # Démarrer l'environnement
   ```

3. **Tester** régulièrement
   ```bash
   npm test
   npm run lint
   ```

4. **Commiter** vos changements
   ```bash
   git add .
   git commit -m "feat: ajouter nouvelle fonctionnalité"
   ```

### Hot Reload

- **Backend** : Rechargement automatique avec `nodemon`
- **Mobile** : Fast Refresh activé par défaut
- **Base de données** : Persistance des données entre redémarrages

## 🧪 Tests

### Exécuter les Tests

```bash
# Tous les tests
npm test

# Tests backend uniquement
npm run backend:test

# Tests mobile uniquement
npm run mobile:test

# Tests avec couverture
npm run backend:test -- --coverage
npm run mobile:test -- --coverage

# Tests en mode watch
npm run backend:test -- --watch
npm run mobile:test -- --watch
```

### Types de Tests

- **Unitaires** : Tests des fonctions individuelles
- **Intégration** : Tests des API endpoints
- **E2E** : Tests end-to-end de l'application mobile
- **Performance** : Tests de charge et performance

## 🔧 Dépannage

### Problèmes Courants

#### 1. Erreur de Port Déjà Utilisé

```bash
# Trouver le processus utilisant le port 3000
lsof -ti:3000

# Tuer le processus
kill -9 $(lsof -ti:3000)

# Ou changer le port dans backend/.env
PORT=3001
```

#### 2. Problèmes de Dépendances

```bash
# Nettoyer et réinstaller
npm run clean
npm run install:all

# Ou manuellement
rm -rf node_modules backend/node_modules mobile/node_modules
npm run install:all
```

#### 3. Erreurs de Base de Données

```bash
# Redémarrer les services Docker
npm run docker:down
npm run docker:up

# Vérifier les logs
npm run docker:logs
```

#### 4. Problèmes React Native

```bash
# Nettoyer le cache Metro
cd mobile
npx react-native start --reset-cache

# Nettoyer les builds
npm run clean

# Réinstaller les pods (iOS)
cd ios && pod install && cd ..
```

### Logs et Debugging

```bash
# Logs backend
tail -f backend/logs/app.log

# Logs Docker
docker-compose logs -f

# Debug React Native
npx react-native log-android  # Android
npx react-native log-ios      # iOS
```

### Support

Si vous rencontrez des problèmes :

1. **Consultez** la [FAQ](https://docs.nowee.app/faq)
2. **Recherchez** dans les [issues GitHub](https://github.com/nowee-app/nowee/issues)
3. **Créez** une nouvelle issue si nécessaire
4. **Rejoignez** notre [Discord](https://discord.gg/nowee) pour de l'aide

## 🎯 Prochaines Étapes

Maintenant que votre environnement est configuré :

1. 📖 Lisez la [documentation API](api-documentation.md)
2. 🤝 Consultez le [guide de contribution](contributing.md)
3. 🏗️ Explorez l'[architecture](architecture.md)
4. 🚢 Apprenez le [déploiement](deployment-guide.md)

Bon développement avec Nowee ! 🚀
