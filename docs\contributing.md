# Guide de Contribution - Nowee

Merci de votre intérêt pour contribuer à Nowee ! Ce guide vous aidera à démarrer.

## 🌟 Comment Contribuer

### Types de Contributions

Nous accueillons plusieurs types de contributions :

- 🐛 **Corrections de bugs**
- ✨ **Nouvelles fonctionnalités**
- 📚 **Amélioration de la documentation**
- 🌍 **Traductions**
- 🧪 **Tests**
- 🎨 **Améliorations UI/UX**

### Avant de Commencer

1. **Consultez les issues existantes** pour éviter les doublons
2. **Créez une issue** pour discuter des changements majeurs
3. **Lisez le code de conduite** (CODE_OF_CONDUCT.md)

## 🚀 Configuration de l'Environnement

### Prérequis

- Node.js 18+
- npm 9+
- Docker & Docker Compose
- Git
- React Native CLI (pour le mobile)

### Installation

```bash
# Cloner le repository
git clone https://github.com/nowee-app/nowee.git
cd nowee

# Installer les dépendances
npm run install:all

# Copier les fichiers d'environnement
cp backend/.env.example backend/.env
cp mobile/.env.example mobile/.env

# Démarrer les services de développement
npm run docker:up

# Démarrer l'application
npm run dev
```

## 📝 Processus de Contribution

### 1. Fork et Clone

```bash
# Fork le repository sur GitHub
# Puis cloner votre fork
git clone https://github.com/VOTRE-USERNAME/nowee.git
cd nowee

# Ajouter le repository original comme remote
git remote add upstream https://github.com/nowee-app/nowee.git
```

### 2. Créer une Branche

```bash
# Créer une branche pour votre fonctionnalité
git checkout -b feature/nom-de-votre-fonctionnalite

# Ou pour un bug fix
git checkout -b fix/description-du-bug
```

### 3. Développer

- Suivez les conventions de code existantes
- Ajoutez des tests pour vos changements
- Mettez à jour la documentation si nécessaire
- Testez vos changements localement

### 4. Commit

Utilisez des messages de commit clairs et descriptifs :

```bash
# Format recommandé
git commit -m "type(scope): description

Explication détaillée si nécessaire

Fixes #123"
```

**Types de commit :**
- `feat`: nouvelle fonctionnalité
- `fix`: correction de bug
- `docs`: documentation
- `style`: formatage, point-virgules manquants, etc.
- `refactor`: refactoring du code
- `test`: ajout de tests
- `chore`: maintenance

### 5. Push et Pull Request

```bash
# Push vers votre fork
git push origin feature/nom-de-votre-fonctionnalite

# Créer une Pull Request sur GitHub
```

## 🧪 Tests

### Backend

```bash
cd backend
npm test                # Tests unitaires
npm run test:watch      # Tests en mode watch
npm run test:coverage   # Couverture de code
```

### Mobile

```bash
cd mobile
npm test                # Tests unitaires
npm run test:e2e        # Tests end-to-end
```

## 📋 Standards de Code

### Backend (Node.js/TypeScript)

- Utilisez TypeScript strict
- Suivez les règles ESLint configurées
- Documentez les fonctions publiques avec JSDoc
- Utilisez des noms de variables descriptifs
- Gérez les erreurs de manière appropriée

### Mobile (React Native/TypeScript)

- Utilisez des composants fonctionnels avec hooks
- Suivez les conventions React Native
- Utilisez Zustand pour la gestion d'état
- Optimisez les performances (memo, useMemo, useCallback)

### Style de Code

```typescript
// ✅ Bon
interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

const getUserProfile = async (userId: string): Promise<UserProfile> => {
  try {
    const user = await userService.getById(userId);
    return user;
  } catch (error) {
    logger.error('Failed to get user profile:', error);
    throw new Error('Unable to fetch user profile');
  }
};

// ❌ Éviter
const getUser = (id) => {
  return userService.getById(id);
};
```

## 🌍 Internationalisation

### Ajouter une Nouvelle Langue

1. Créer le fichier de traduction dans `mobile/src/locales/`
2. Ajouter la langue dans `mobile/src/utils/i18n.ts`
3. Mettre à jour les types TypeScript
4. Tester avec la nouvelle langue

### Format des Traductions

```json
{
  "common": {
    "save": "Enregistrer",
    "cancel": "Annuler",
    "loading": "Chargement..."
  },
  "auth": {
    "login": "Se connecter",
    "register": "S'inscrire",
    "forgotPassword": "Mot de passe oublié ?"
  }
}
```

## 🐛 Signaler des Bugs

### Informations à Inclure

- **Description claire** du problème
- **Étapes pour reproduire** le bug
- **Comportement attendu** vs **comportement actuel**
- **Captures d'écran** si applicable
- **Environnement** (OS, version de l'app, etc.)
- **Logs d'erreur** si disponibles

### Template d'Issue

```markdown
## Description
Brève description du bug

## Étapes pour Reproduire
1. Aller à '...'
2. Cliquer sur '...'
3. Faire défiler jusqu'à '...'
4. Voir l'erreur

## Comportement Attendu
Ce qui devrait se passer

## Comportement Actuel
Ce qui se passe réellement

## Captures d'Écran
Si applicable, ajoutez des captures d'écran

## Environnement
- OS: [ex. iOS 17, Android 13]
- Version de l'app: [ex. 1.0.0]
- Appareil: [ex. iPhone 14, Samsung Galaxy S23]
```

## 💡 Proposer des Fonctionnalités

### Template de Proposition

```markdown
## Résumé
Brève description de la fonctionnalité

## Motivation
Pourquoi cette fonctionnalité est-elle nécessaire ?

## Description Détaillée
Description complète de la fonctionnalité

## Alternatives Considérées
Autres solutions envisagées

## Impact
- Impact sur les utilisateurs
- Impact technique
- Impact sur les performances
```

## 📚 Documentation

### Mettre à Jour la Documentation

- README.md pour les changements généraux
- docs/ pour la documentation détaillée
- Commentaires dans le code pour les fonctions complexes
- CHANGELOG.md pour les nouvelles versions

## 🎯 Priorités de Développement

### Fonctionnalités Prioritaires

1. **Sécurité et confidentialité**
2. **Performance et optimisation**
3. **Accessibilité**
4. **Support multilingue**
5. **Nouvelles fonctionnalités**

### Zones d'Amélioration

- Interface utilisateur intuitive
- Algorithmes de matching intelligents
- Intégration IA avancée
- Mode hors ligne
- Tests automatisés

## 🤝 Communauté

### Canaux de Communication

- **GitHub Issues** : Bugs et fonctionnalités
- **GitHub Discussions** : Questions générales
- **Discord** : Chat en temps réel
- **Email** : <EMAIL>

### Code de Conduite

Nous nous engageons à maintenir un environnement accueillant et inclusif. Consultez notre [Code de Conduite](CODE_OF_CONDUCT.md).

## 🏆 Reconnaissance

Les contributeurs sont reconnus dans :
- README.md principal
- Page des contributeurs
- Notes de version
- Réseaux sociaux

## 📞 Besoin d'Aide ?

N'hésitez pas à :
- Ouvrir une issue avec le label "question"
- Rejoindre notre Discord
- Envoyer un email à <EMAIL>

Merci de contribuer à Nowee ! 🙏
